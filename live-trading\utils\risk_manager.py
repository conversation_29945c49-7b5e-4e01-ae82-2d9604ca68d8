#!/usr/bin/env python3
"""
Dynamic Risk Management System
Advanced risk management with adaptive position sizing and drawdown protection

Features:
- Dynamic risk percentage calculation
- Streak-based adjustments
- Equity milestone scaling
- Drawdown protection
- Performance-based adaptation
- Real-time risk monitoring
"""

import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Deque
from collections import deque
import statistics

class RiskManager:
    def __init__(self, initial_balance: float = 1000.0, 
                 max_risk_per_trade: float = 0.06,
                 min_risk_per_trade: float = 0.01,
                 base_risk: float = 0.04):
        
        # Risk parameters
        self.initial_balance = initial_balance
        self.max_risk_per_trade = max_risk_per_trade
        self.min_risk_per_trade = min_risk_per_trade
        self.base_risk = base_risk
        
        # Account state
        self.current_balance = initial_balance
        self.peak_balance = initial_balance
        self.equity_history = deque(maxlen=1000)
        self.equity_history.append(initial_balance)
        
        # Trade tracking
        self.trade_outcomes = deque(maxlen=100)  # Last 100 trades
        self.win_streak = 0
        self.loss_streak = 0
        self.total_trades = 0
        self.winning_trades = 0
        
        # Performance metrics
        self.daily_returns = deque(maxlen=30)  # Last 30 days
        self.monthly_returns = deque(maxlen=12)  # Last 12 months
        self.last_daily_update = datetime.now().date()
        
        # Risk state
        self.current_risk_percentage = base_risk
        self.max_drawdown = 0.0
        self.current_drawdown = 0.0
        self.risk_reduction_active = False
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Logging
        self.logger = logging.getLogger(__name__)
        
    def update_balance(self, new_balance: float):
        """Update current balance and related metrics"""
        with self.lock:
            previous_balance = self.current_balance
            self.current_balance = new_balance
            self.equity_history.append(new_balance)
            
            # Update peak balance
            if new_balance > self.peak_balance:
                self.peak_balance = new_balance
                
            # Calculate drawdown
            self.current_drawdown = (self.peak_balance - new_balance) / self.peak_balance
            if self.current_drawdown > self.max_drawdown:
                self.max_drawdown = self.current_drawdown
                
            # Update daily returns if needed
            self._update_daily_returns()
            
            self.logger.debug(f"Balance updated: ${previous_balance:.2f} -> ${new_balance:.2f}")
            
    def record_trade_outcome(self, profit_loss: float):
        """Record the outcome of a trade"""
        with self.lock:
            self.total_trades += 1
            self.trade_outcomes.append(profit_loss)
            
            # Update balance
            new_balance = self.current_balance + profit_loss
            self.update_balance(new_balance)
            
            # Update streaks
            if profit_loss > 0:
                self.winning_trades += 1
                self.win_streak += 1
                self.loss_streak = 0
            else:
                self.win_streak = 0
                self.loss_streak += 1
                
            # Recalculate risk percentage
            self._calculate_dynamic_risk()
            
            self.logger.info(f"Trade recorded: P&L=${profit_loss:.2f}, Balance=${new_balance:.2f}, Risk={self.current_risk_percentage:.1%}")
            
    def calculate_risk_amount(self, balance: float = None) -> float:
        """Calculate the dollar amount to risk on next trade"""
        if balance is None:
            balance = self.current_balance
            
        with self.lock:
            risk_amount = balance * self.current_risk_percentage
            
            # Apply additional safety checks
            max_allowed = balance * self.max_risk_per_trade
            min_allowed = balance * self.min_risk_per_trade
            
            risk_amount = max(min_allowed, min(risk_amount, max_allowed))
            
            return risk_amount
            
    def _calculate_dynamic_risk(self):
        """Calculate dynamic risk percentage based on multiple factors"""
        with self.lock:
            risk = self.base_risk
            
            # 1. Streak-based adjustment
            streak_factor = self._get_streak_factor()
            
            # 2. Equity milestone adjustment
            equity_factor = self._get_equity_factor()
            
            # 3. Recent performance adjustment
            performance_factor = self._get_performance_factor()
            
            # 4. Drawdown protection
            drawdown_factor = self._get_drawdown_factor()
            
            # 5. Volatility adjustment
            volatility_factor = self._get_volatility_factor()
            
            # Combine all factors
            risk = risk * streak_factor * equity_factor * performance_factor * drawdown_factor * volatility_factor
            
            # Apply bounds
            self.current_risk_percentage = max(self.min_risk_per_trade, min(risk, self.max_risk_per_trade))
            
    def _get_streak_factor(self) -> float:
        """Calculate risk adjustment based on win/loss streaks"""
        if self.win_streak >= 5:
            return 1.3  # Increase risk after long win streak
        elif self.win_streak >= 3:
            return 1.15
        elif self.loss_streak >= 3:
            return 0.7  # Reduce risk after losses
        elif self.loss_streak >= 1:
            return 0.85
        else:
            return 1.0
            
    def _get_equity_factor(self) -> float:
        """Calculate risk adjustment based on account size"""
        if self.current_balance < 1000:
            return 1.0
        elif self.current_balance < 10000:
            return 0.95
        elif self.current_balance < 100000:
            return 0.9
        elif self.current_balance < 1000000:
            return 0.85
        else:
            return 0.8
            
    def _get_performance_factor(self) -> float:
        """Calculate risk adjustment based on recent performance"""
        if len(self.trade_outcomes) < 20:
            return 1.0
            
        recent_trades = list(self.trade_outcomes)[-20:]
        win_rate = sum(1 for trade in recent_trades if trade > 0) / len(recent_trades)
        
        if win_rate > 0.9:
            return 1.2
        elif win_rate > 0.8:
            return 1.1
        elif win_rate < 0.5:
            return 0.8
        else:
            return 1.0
            
    def _get_drawdown_factor(self) -> float:
        """Calculate risk adjustment based on current drawdown"""
        if self.current_drawdown > 0.2:
            return 0.6  # Severe drawdown protection
        elif self.current_drawdown > 0.1:
            return 0.8
        elif self.current_drawdown > 0.05:
            return 0.9
        else:
            return 1.0
            
    def _get_volatility_factor(self) -> float:
        """Calculate risk adjustment based on equity volatility"""
        if len(self.equity_history) < 10:
            return 1.0
            
        recent_equity = list(self.equity_history)[-10:]
        returns = [(recent_equity[i] - recent_equity[i-1]) / recent_equity[i-1] 
                  for i in range(1, len(recent_equity))]
        
        if len(returns) < 2:
            return 1.0
            
        volatility = statistics.stdev(returns)
        
        # Reduce risk in high volatility periods
        if volatility > 0.05:  # 5% volatility
            return 0.8
        elif volatility > 0.03:  # 3% volatility
            return 0.9
        else:
            return 1.0
            
    def _update_daily_returns(self):
        """Update daily returns tracking"""
        today = datetime.now().date()
        
        if today != self.last_daily_update and len(self.equity_history) >= 2:
            # Calculate daily return
            yesterday_balance = list(self.equity_history)[-2]
            today_balance = self.current_balance
            
            daily_return = (today_balance - yesterday_balance) / yesterday_balance
            self.daily_returns.append(daily_return)
            
            self.last_daily_update = today
            
    def get_risk_metrics(self) -> Dict:
        """Get comprehensive risk metrics"""
        with self.lock:
            win_rate = (self.winning_trades / self.total_trades * 100) if self.total_trades > 0 else 0
            
            # Calculate profit factor
            winning_trades = [trade for trade in self.trade_outcomes if trade > 0]
            losing_trades = [trade for trade in self.trade_outcomes if trade < 0]
            
            total_wins = sum(winning_trades) if winning_trades else 0
            total_losses = abs(sum(losing_trades)) if losing_trades else 1e-6
            profit_factor = total_wins / total_losses if total_losses > 0 else 0
            
            # Calculate Sharpe ratio (simplified)
            if len(self.daily_returns) > 1:
                avg_return = statistics.mean(self.daily_returns)
                return_std = statistics.stdev(self.daily_returns)
                sharpe_ratio = avg_return / return_std if return_std > 0 else 0
            else:
                sharpe_ratio = 0
                
            return {
                'current_balance': self.current_balance,
                'initial_balance': self.initial_balance,
                'peak_balance': self.peak_balance,
                'current_drawdown_percent': self.current_drawdown * 100,
                'max_drawdown_percent': self.max_drawdown * 100,
                'current_risk_percent': self.current_risk_percentage * 100,
                'win_rate_percent': win_rate,
                'profit_factor': profit_factor,
                'sharpe_ratio': sharpe_ratio,
                'total_trades': self.total_trades,
                'win_streak': self.win_streak,
                'loss_streak': self.loss_streak,
                'risk_amount_next_trade': self.calculate_risk_amount()
            }
            
    def should_stop_trading(self) -> bool:
        """Determine if trading should be stopped due to risk limits"""
        with self.lock:
            # Stop if drawdown is too severe
            if self.current_drawdown > 0.25:  # 25% drawdown
                return True
                
            # Stop if balance is too low
            if self.current_balance < self.initial_balance * 0.5:  # 50% of initial
                return True
                
            # Stop if too many consecutive losses
            if self.loss_streak >= 10:
                return True
                
            return False
            
    def reset_risk_state(self):
        """Reset risk management state (for new trading session)"""
        with self.lock:
            self.current_risk_percentage = self.base_risk
            self.win_streak = 0
            self.loss_streak = 0
            self.risk_reduction_active = False
            
        self.logger.info("Risk state reset")
        
    def export_performance_data(self) -> Dict:
        """Export performance data for analysis"""
        with self.lock:
            return {
                'equity_history': list(self.equity_history),
                'trade_outcomes': list(self.trade_outcomes),
                'daily_returns': list(self.daily_returns),
                'risk_metrics': self.get_risk_metrics(),
                'timestamps': [datetime.now() - timedelta(days=i) for i in range(len(self.equity_history))]
            }

class DynamicRiskManager(RiskManager):
    """Enhanced risk manager with additional dynamic features"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Additional dynamic features
        self.market_condition = 'normal'  # 'trending', 'ranging', 'volatile'
        self.time_based_risk = True
        self.session_performance = {'trades': 0, 'pnl': 0.0}
        
    def adjust_for_market_condition(self, condition: str):
        """Adjust risk based on market conditions"""
        with self.lock:
            self.market_condition = condition
            
            if condition == 'volatile':
                self.current_risk_percentage *= 0.8
            elif condition == 'trending':
                self.current_risk_percentage *= 1.1
            elif condition == 'ranging':
                self.current_risk_percentage *= 0.9
                
            # Apply bounds
            self.current_risk_percentage = max(
                self.min_risk_per_trade, 
                min(self.current_risk_percentage, self.max_risk_per_trade)
            )
            
    def get_time_based_risk_factor(self) -> float:
        """Adjust risk based on time of day/session"""
        if not self.time_based_risk:
            return 1.0
            
        current_hour = datetime.now().hour
        
        # Reduce risk during low liquidity hours
        if current_hour in [22, 23, 0, 1, 2, 3, 4, 5]:  # Night hours
            return 0.8
        elif current_hour in [6, 7, 8, 9]:  # Morning session
            return 1.1
        else:
            return 1.0

# Example usage
if __name__ == "__main__":
    # Create risk manager
    risk_manager = DynamicRiskManager(
        initial_balance=1000.0,
        max_risk_per_trade=0.06,
        min_risk_per_trade=0.01
    )
    
    print("🛡️ Testing Dynamic Risk Manager...")
    
    # Simulate some trades
    import random
    
    for i in range(50):
        # Simulate trade outcome
        profit_loss = random.uniform(-50, 100)  # Random P&L
        risk_manager.record_trade_outcome(profit_loss)
        
        if i % 10 == 0:
            metrics = risk_manager.get_risk_metrics()
            print(f"\nTrade {i}: Balance=${metrics['current_balance']:.2f}, Risk={metrics['current_risk_percent']:.1f}%")
            
    # Final metrics
    final_metrics = risk_manager.get_risk_metrics()
    print(f"\n📊 Final Metrics:")
    for key, value in final_metrics.items():
        print(f"  {key}: {value}")
        
    print("✅ Risk manager test completed")
