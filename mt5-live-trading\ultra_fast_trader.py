#!/usr/bin/env python3
"""
ULTRA-FAST LIVE UT BOT TRADING SYSTEM
Maximum performance optimization for <100ms total processing

⚡ PERFORMANCE TARGETS:
- Renko processing: <5ms
- UT Bot signals: <10ms  
- Order execution: <50ms
- Total pipeline: <100ms (5x faster than requirement!)

🚀 OPTIMIZATIONS:
- Numba JIT compilation
- Pure NumPy vectorization
- Zero pandas overhead
- Memory-efficient arrays
- Pre-compiled functions

⚠️  WARNING: THIS TRADES REAL MONEY ⚠️
"""

import sys
import os
import time
import logging
import signal
import numpy as np
from datetime import datetime

# Add core modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))

# Import ultra-fast modules
from mt5_connection import MT5Connection
from ultra_fast_renko import UltraFastRenko
from ultra_fast_utbot import UltraFastUTBot, UltraFastSignalManager
from order_manager import OrderManager
from trading_config import *

class UltraFastTrader:
    """Ultra-optimized live trading system"""
    
    def __init__(self):
        # System components
        self.mt5 = None
        self.renko = None
        self.utbot = None
        self.signal_manager = None
        self.order_manager = None
        
        # Performance tracking
        self.tick_count = 0
        self.signal_count = 0
        self.total_processing_times = []
        self.pipeline_times = {
            'renko': [],
            'utbot': [],
            'orders': [],
            'total': []
        }
        
        # Trading state
        self.running = False
        self.last_tick_time = None
        self.start_time = None
        
        # Setup logging
        self.setup_ultra_fast_logging()
        self.logger = logging.getLogger(__name__)
        
        # Signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def setup_ultra_fast_logging(self):
        """Minimal logging for maximum performance"""
        os.makedirs('logs', exist_ok=True)
        
        # Minimal log format for speed
        log_format = '%(asctime)s | %(levelname)s | %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler('logs/ultra_fast_trading.log'),
                logging.StreamHandler()
            ]
        )
    
    def initialize_ultra_fast_system(self):
        """Initialize ultra-optimized trading system"""
        try:
            self.logger.info("⚡ INITIALIZING ULTRA-FAST TRADING SYSTEM")
            self.logger.info("🎯 Target: <100ms total processing per tick")
            self.logger.info("=" * 60)
            
            # Initialize MT5 connection
            self.logger.info("🔌 Connecting to MT5...")
            self.mt5 = MT5Connection(
                login=MT5_CONFIG['login'],
                password=MT5_CONFIG['password'],
                server=MT5_CONFIG['server']
            )
            
            if not self.mt5.connect() or not self.mt5.find_step_index_symbol():
                return False
            
            # Initialize ultra-fast Renko
            self.logger.info("⚡ Initializing Ultra-Fast Renko...")
            self.renko = UltraFastRenko(
                brick_size=RENKO_CONFIG['brick_size'],
                max_history=RENKO_CONFIG['max_history']
            )
            
            # Initialize ultra-fast UT Bot
            self.logger.info("🚀 Initializing Ultra-Fast UT Bot...")
            self.utbot = UltraFastUTBot(
                atr_period=UTBOT_CONFIG['atr_period'],
                sensitivity=UTBOT_CONFIG['sensitivity'],
                min_periods=UTBOT_CONFIG['min_bricks']
            )
            
            self.signal_manager = UltraFastSignalManager(self.utbot)
            
            # Initialize order manager
            self.logger.info("💼 Initializing Order Manager...")
            self.order_manager = OrderManager(
                mt5_connection=self.mt5,
                initial_lot_size=RISK_CONFIG['initial_lot_size'],
                max_lot_size=RISK_CONFIG['max_lot_size']
            )
            
            self.logger.info("✅ ULTRA-FAST SYSTEM READY!")
            self.logger.info("🚨 MAXIMUM PERFORMANCE MODE ACTIVATED!")
            self.logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            self.logger.error(f"💥 Ultra-fast initialization failed: {e}")
            return False
    
    def process_tick_ultra_fast(self):
        """Ultra-fast tick processing pipeline"""
        pipeline_start = time.perf_counter()
        
        try:
            # STEP 1: Get tick (<1ms)
            tick = self.mt5.get_tick()
            if not tick or self.last_tick_time == tick['time']:
                return False
            
            self.last_tick_time = tick['time']
            self.tick_count += 1
            
            # STEP 2: Ultra-fast Renko processing (<5ms)
            renko_start = time.perf_counter()
            
            new_bricks = self.renko.add_tick_ultra_fast(tick['time'], tick['bid'])
            
            renko_time = (time.perf_counter() - renko_start) * 1000
            self.pipeline_times['renko'].append(renko_time)
            
            if new_bricks == 0:
                return False  # No new bricks, skip signal processing
            
            # STEP 3: Ultra-fast signal generation (<10ms)
            utbot_start = time.perf_counter()
            
            # Get NumPy arrays (ultra-fast)
            renko_arrays = self.renko.get_numpy_arrays(last_n=100)
            
            # Generate signal
            signal = self.signal_manager.process_renko_arrays(renko_arrays)
            
            utbot_time = (time.perf_counter() - utbot_start) * 1000
            self.pipeline_times['utbot'].append(utbot_time)
            
            # STEP 4: Ultra-fast order execution (<50ms)
            if signal and signal['signal_type'] in ['BUY', 'SELL']:
                order_start = time.perf_counter()
                
                self.signal_count += 1
                
                # Check risk limits
                if self.order_manager.check_risk_limits():
                    success = self.order_manager.process_signal(signal)
                    
                    if success:
                        self.logger.info(f"⚡ ULTRA-FAST {signal['signal_type']} EXECUTED!")
                    else:
                        self.logger.error(f"❌ Order execution failed")
                else:
                    self.logger.warning("⚠️ Risk limits - skipping trade")
                
                order_time = (time.perf_counter() - order_start) * 1000
                self.pipeline_times['orders'].append(order_time)
            
            # STEP 5: Performance tracking
            total_time = (time.perf_counter() - pipeline_start) * 1000
            self.pipeline_times['total'].append(total_time)
            
            # Performance alerts
            if total_time > 100.0:
                self.logger.warning(f"⚠️ Slow pipeline: {total_time:.1f}ms > 100ms target")
            elif renko_time > 5.0:
                self.logger.warning(f"⚠️ Slow Renko: {renko_time:.1f}ms > 5ms target")
            elif utbot_time > 10.0:
                self.logger.warning(f"⚠️ Slow UT Bot: {utbot_time:.1f}ms > 10ms target")
            
            # Log performance every 100 ticks
            if self.tick_count % 100 == 0:
                self.log_ultra_fast_stats()
            
            return True
            
        except Exception as e:
            self.logger.error(f"💥 Ultra-fast processing error: {e}")
            return False
    
    def log_ultra_fast_stats(self):
        """Log ultra-fast performance statistics"""
        try:
            # Calculate recent performance (last 100 operations)
            recent_total = self.pipeline_times['total'][-100:]
            recent_renko = self.pipeline_times['renko'][-100:]
            recent_utbot = self.pipeline_times['utbot'][-100:]
            
            if recent_total:
                avg_total = np.mean(recent_total)
                max_total = np.max(recent_total)
                under_100ms = (np.sum(np.array(recent_total) < 100.0) / len(recent_total)) * 100
            else:
                avg_total = max_total = under_100ms = 0
            
            # Get component stats
            renko_stats = self.renko.get_performance_stats()
            utbot_stats = self.utbot.get_performance_stats()
            
            self.logger.info("⚡ ULTRA-FAST PERFORMANCE STATS")
            self.logger.info(f"   Ticks processed: {self.tick_count}")
            self.logger.info(f"   Signals generated: {self.signal_count}")
            self.logger.info(f"   Avg total time: {avg_total:.1f}ms")
            self.logger.info(f"   Max total time: {max_total:.1f}ms")
            self.logger.info(f"   Under 100ms: {under_100ms:.1f}%")
            
            if renko_stats:
                self.logger.info(f"   Renko avg: {renko_stats['avg_processing_ms']:.1f}ms")
                self.logger.info(f"   Renko <5ms: {renko_stats['under_5ms_pct']:.1f}%")
            
            if utbot_stats:
                self.logger.info(f"   UT Bot avg: {utbot_stats['avg_processing_ms']:.1f}ms")
                self.logger.info(f"   UT Bot <10ms: {utbot_stats['under_10ms_pct']:.1f}%")
            
        except Exception as e:
            self.logger.error(f"Error logging ultra-fast stats: {e}")
    
    def run_ultra_fast(self):
        """Ultra-fast main trading loop"""
        try:
            self.logger.info("🚀 STARTING ULTRA-FAST LIVE TRADING")
            self.logger.info("⚡ MAXIMUM PERFORMANCE MODE")
            self.logger.info("🎯 Target: <100ms per tick")
            self.logger.info("Press Ctrl+C to stop")
            self.logger.info("-" * 60)
            
            self.running = True
            self.start_time = time.time()
            
            while self.running:
                # Ultra-fast tick processing
                self.process_tick_ultra_fast()
                
                # Minimal delay (50ms for responsiveness)
                time.sleep(0.05)
            
        except KeyboardInterrupt:
            self.logger.info("🛑 Ultra-fast trading stopped by user")
        except Exception as e:
            self.logger.error(f"💥 Ultra-fast trading error: {e}")
        finally:
            self.shutdown_ultra_fast()
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"🛑 Signal {signum} - ultra-fast shutdown...")
        self.running = False
    
    def shutdown_ultra_fast(self):
        """Ultra-fast graceful shutdown"""
        try:
            self.logger.info("🔄 Ultra-fast shutdown...")
            
            # Final performance stats
            self.log_ultra_fast_stats()
            
            # Show final summary
            if self.pipeline_times['total']:
                total_times = self.pipeline_times['total']
                avg_time = np.mean(total_times)
                max_time = np.max(total_times)
                under_target = (np.sum(np.array(total_times) < 100.0) / len(total_times)) * 100
                
                self.logger.info("📊 FINAL ULTRA-FAST PERFORMANCE:")
                self.logger.info(f"   Average processing: {avg_time:.1f}ms")
                self.logger.info(f"   Maximum processing: {max_time:.1f}ms")
                self.logger.info(f"   Under 100ms target: {under_target:.1f}%")
            
            # Disconnect
            if self.mt5:
                self.mt5.disconnect()
            
            self.logger.info("✅ Ultra-fast shutdown completed")
            
        except Exception as e:
            self.logger.error(f"Error in ultra-fast shutdown: {e}")

def main():
    """Ultra-fast main entry point"""
    print("⚡ ULTRA-FAST LIVE UT BOT TRADING SYSTEM")
    print("🎯 TARGET: <100ms TOTAL PROCESSING")
    print("⚠️  WARNING: THIS TRADES REAL MONEY!")
    print("=" * 50)
    
    # Create ultra-fast trader
    trader = UltraFastTrader()
    
    if trader.initialize_ultra_fast_system():
        trader.run_ultra_fast()
    else:
        print("❌ Failed to initialize ultra-fast system")

if __name__ == "__main__":
    main()
