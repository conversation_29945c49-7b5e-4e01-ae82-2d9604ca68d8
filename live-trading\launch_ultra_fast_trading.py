#!/usr/bin/env python3
"""
Ultra-Fast Live Trading System Launcher
Complete integration with Deriv WebSocket + MT5 Demo Account

Account Details:
- MT5 Login: ********
- Server: Deriv-Demo
- Symbol: stpRNG (stepRNG 100)
- Strategy: UT Bot with ultra-fast libraries

Performance Targets:
- Total latency: <500ms
- Signal calculation: <50ms
- Order execution: <200ms
"""

import asyncio
import uvloop
import logging
import signal
import sys
import os
import time
from datetime import datetime
from typing import Dict, Optional

# Set ultra-fast event loop
asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.trading_config import create_config, LiveTradingConfig
from core.ultra_fast_websocket import UltraFastTickCollector, TickData
from strategies.ultra_fast_ut_bot import UltraFastUTBot
from mt5.mt5_manager import MT5Manager, TradeRequest
from utils.risk_manager import DynamicRiskManager

class UltraFastTradingSystem:
    """Complete ultra-fast trading system with Deriv + MT5 integration"""
    
    def __init__(self):
        # Load configuration with MT5 credentials
        self.config = create_config("production")  # Use production settings
        
        # Core components
        self.tick_collector = None
        self.ut_bot = None
        self.mt5_manager = None
        self.risk_manager = None
        
        # Trading state
        self.running = False
        self.start_time = None
        self.last_signal_time = 0
        self.signal_cooldown = 5  # seconds
        
        # Performance tracking
        self.stats = {
            'ticks_received': 0,
            'signals_generated': 0,
            'trades_executed': 0,
            'total_pnl': 0.0,
            'start_time': None
        }
        
        # Setup logging
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
    def setup_logging(self):
        """Setup comprehensive logging"""
        # Create logs directory
        log_dir = os.path.join(os.path.dirname(__file__), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # Setup file handler
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(log_dir, f"ultra_fast_trading_{timestamp}.log")
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
    async def initialize(self) -> bool:
        """Initialize all components"""
        try:
            self.logger.info("🚀 Initializing Ultra-Fast Trading System...")
            self.logger.info(f"MT5 Account: {self.config.mt5.login}")
            self.logger.info(f"MT5 Server: {self.config.mt5.server}")
            self.logger.info(f"Trading Symbol: {self.config.trading.symbol}")
            
            # Initialize MT5 Manager
            self.logger.info("🔌 Connecting to MT5...")
            self.mt5_manager = MT5Manager(
                login=self.config.mt5.login,
                password=self.config.mt5.password,
                server=self.config.mt5.server
            )
            
            if not self.mt5_manager.initialize():
                raise Exception("Failed to initialize MT5 connection")
                
            # Get account info
            account_info = self.mt5_manager.update_account_info()
            self.logger.info(f"✅ MT5 Connected - Balance: ${account_info.get('balance', 0):.2f}")
            
            # Initialize Risk Manager
            self.risk_manager = DynamicRiskManager(
                initial_balance=account_info.get('balance', 1000),
                max_risk_per_trade=self.config.risk.max_risk_percent / 100,
                min_risk_per_trade=self.config.risk.min_risk_percent / 100
            )
            
            # Initialize UT Bot
            self.logger.info("🧠 Initializing Ultra-Fast UT Bot...")
            self.ut_bot = UltraFastUTBot(
                atr_period=self.config.trading.atr_period,
                sensitivity=self.config.trading.sensitivity,
                buffer_size=1000
            )
            
            # Initialize Tick Collector
            self.logger.info("📡 Initializing Ultra-Fast WebSocket Collector...")
            self.tick_collector = UltraFastTickCollector(
                app_id=self.config.deriv.app_id,
                symbols=[self.config.trading.symbol],
                buffer_size=self.config.performance.tick_buffer_size
            )
            
            # Add tick callback
            self.tick_collector.add_tick_callback(self.on_tick_received)
            
            self.logger.info("✅ All components initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Initialization failed: {e}")
            return False
            
    async def on_tick_received(self, tick: TickData):
        """Process incoming tick data"""
        try:
            if not self.running:
                return
                
            self.stats['ticks_received'] += 1
            
            # Process tick through UT Bot
            signal = self.ut_bot.add_tick(tick.price)
            
            if signal:
                await self.handle_signal(signal, tick)
                
        except Exception as e:
            self.logger.error(f"❌ Tick processing error: {e}")
            
    async def handle_signal(self, signal: Dict, tick: TickData):
        """Handle trading signal"""
        try:
            current_time = time.time()
            
            # Check signal cooldown
            if current_time - self.last_signal_time < self.signal_cooldown:
                return
                
            self.last_signal_time = current_time
            self.stats['signals_generated'] += 1
            
            self.logger.info(f"📊 Signal: {signal['type']} @ {signal['price']:.5f} "
                           f"(Method: {signal.get('calculation_method', 'Unknown')})")
            
            # Execute trade
            await self.execute_trade(signal)
            
        except Exception as e:
            self.logger.error(f"❌ Signal handling error: {e}")
            
    async def execute_trade(self, signal: Dict):
        """Execute trade based on signal"""
        try:
            # Get current account info
            account_info = self.mt5_manager.update_account_info()
            current_balance = account_info.get('balance', 1000)
            
            # Calculate risk amount
            risk_amount = self.risk_manager.calculate_risk_amount(current_balance)
            
            # Calculate position size (simplified for demo)
            # For stpRNG, we'll use a fixed lot size for testing
            volume = 0.01  # Start with minimum volume
            
            # Close any existing positions first
            existing_positions = self.mt5_manager.get_positions(self.config.trading.symbol)
            for position in existing_positions:
                close_result = self.mt5_manager.close_position(
                    self.config.trading.symbol, 
                    position['identifier']
                )
                if close_result.success:
                    self.logger.info(f"✅ Closed position: {position['identifier']}")
                    
            # Create trade request
            trade_request = TradeRequest(
                symbol=self.config.trading.symbol,
                action=signal['type'],
                volume=volume,
                sl=signal.get('trailing_stop'),
                comment=f"UltraFast_UT_{signal['type']}"
            )
            
            # Execute trade
            self.logger.info(f"⚡ Executing {signal['type']} order...")
            result = self.mt5_manager.send_order(trade_request)
            
            if result.success:
                self.stats['trades_executed'] += 1
                self.logger.info(f"✅ Trade executed: {signal['type']} {volume} @ {result.price} "
                               f"in {result.execution_time_ms:.1f}ms")
                
                # Update risk manager (will be updated when trade closes)
                self.risk_manager.record_trade_outcome(0)  # Placeholder
                
            else:
                self.logger.error(f"❌ Trade failed: {result.error_description}")
                
        except Exception as e:
            self.logger.error(f"❌ Trade execution error: {e}")
            
    async def performance_monitor(self):
        """Monitor system performance"""
        while self.running:
            try:
                await asyncio.sleep(60)  # Update every minute
                
                # Get performance stats
                uptime = time.time() - self.start_time
                tick_rate = self.stats['ticks_received'] / uptime if uptime > 0 else 0
                
                # Get component stats
                tick_stats = self.tick_collector.get_performance_stats()
                ut_bot_stats = self.ut_bot.get_performance_stats()
                mt5_stats = self.mt5_manager.get_performance_stats()
                
                self.logger.info("📊 Performance Update:")
                self.logger.info(f"  Uptime: {uptime/3600:.1f} hours")
                self.logger.info(f"  Ticks: {self.stats['ticks_received']} ({tick_rate:.1f}/sec)")
                self.logger.info(f"  Signals: {self.stats['signals_generated']}")
                self.logger.info(f"  Trades: {self.stats['trades_executed']}")
                self.logger.info(f"  Tick Processing: {tick_stats['average_processing_time_ms']:.2f}ms")
                self.logger.info(f"  UT Bot Calc: {ut_bot_stats['recent_average_ms']:.2f}ms")
                self.logger.info(f"  MT5 Execution: {mt5_stats['average_execution_time_ms']:.1f}ms")
                
                # Check performance targets
                if tick_stats['average_processing_time_ms'] > 10:
                    self.logger.warning("⚠️ Tick processing slower than target (>10ms)")
                if ut_bot_stats['recent_average_ms'] > 50:
                    self.logger.warning("⚠️ UT Bot calculation slower than target (>50ms)")
                if mt5_stats['average_execution_time_ms'] > 200:
                    self.logger.warning("⚠️ MT5 execution slower than target (>200ms)")
                    
            except Exception as e:
                self.logger.error(f"❌ Performance monitoring error: {e}")
                
    async def start(self):
        """Start the ultra-fast trading system"""
        if not await self.initialize():
            raise Exception("Failed to initialize trading system")
            
        self.running = True
        self.start_time = time.time()
        self.stats['start_time'] = self.start_time
        
        self.logger.info("🎯 Starting ultra-fast live trading...")
        self.logger.info("🚀 Performance targets: <500ms total, <50ms signals, <200ms execution")
        self.logger.info("Press Ctrl+C to stop")
        
        # Start performance monitoring
        monitor_task = asyncio.create_task(self.performance_monitor())
        
        try:
            # Start tick collection (this will run indefinitely)
            await self.tick_collector.start()
            
        except KeyboardInterrupt:
            self.logger.info("🛑 Keyboard interrupt received")
        except Exception as e:
            self.logger.error(f"❌ Trading system error: {e}")
        finally:
            monitor_task.cancel()
            await self.stop()
            
    async def stop(self):
        """Stop the trading system"""
        self.logger.info("🛑 Stopping ultra-fast trading system...")
        self.running = False
        
        # Stop tick collector
        if self.tick_collector:
            await self.tick_collector.stop()
            
        # Close MT5 connection
        if self.mt5_manager:
            self.mt5_manager.shutdown()
            
        # Generate final report
        await self.generate_final_report()
        
        self.logger.info("✅ Ultra-fast trading system stopped")
        
    async def generate_final_report(self):
        """Generate final performance report"""
        try:
            uptime = time.time() - self.start_time
            
            self.logger.info("=" * 60)
            self.logger.info("📊 ULTRA-FAST TRADING FINAL REPORT")
            self.logger.info("=" * 60)
            self.logger.info(f"Session Duration: {uptime/3600:.2f} hours")
            self.logger.info(f"Ticks Processed: {self.stats['ticks_received']:,}")
            self.logger.info(f"Signals Generated: {self.stats['signals_generated']}")
            self.logger.info(f"Trades Executed: {self.stats['trades_executed']}")
            
            if self.tick_collector:
                tick_stats = self.tick_collector.get_performance_stats()
                self.logger.info(f"Average Tick Processing: {tick_stats['average_processing_time_ms']:.2f}ms")
                
            if self.ut_bot:
                bot_stats = self.ut_bot.get_performance_stats()
                self.logger.info(f"Average Signal Calculation: {bot_stats['recent_average_ms']:.2f}ms")
                
            if self.mt5_manager:
                mt5_stats = self.mt5_manager.get_performance_stats()
                self.logger.info(f"Average Order Execution: {mt5_stats['average_execution_time_ms']:.1f}ms")
                self.logger.info(f"MT5 Success Rate: {mt5_stats['success_rate_percent']:.1f}%")
                
            self.logger.info("=" * 60)
            
        except Exception as e:
            self.logger.error(f"❌ Report generation error: {e}")

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print("\n🛑 Shutdown signal received...")
    sys.exit(0)

async def main():
    """Main entry point"""
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("🚀 Ultra-Fast Live Trading System")
    print("=" * 50)
    print("Account: ******** (Deriv-Demo)")
    print("Symbol: stpRNG")
    print("Strategy: Ultra-Fast UT Bot")
    print("Target: <500ms total latency")
    print("=" * 50)
    
    # Create and start trading system
    system = UltraFastTradingSystem()
    
    try:
        await system.start()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ System error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
