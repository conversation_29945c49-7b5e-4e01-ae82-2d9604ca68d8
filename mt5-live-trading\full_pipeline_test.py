#!/usr/bin/env python3
"""
Full Pipeline Performance Test
Minimalistic vectorized data processing test:
- 1-second ticks
- Vectorized 0.1 Renko
- UT Bot signal generation
- Signal processing
- Order execution timing
- Target: <500ms data processing (excluding order execution)
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import time
import sys
import os
from datetime import datetime

# Add UT Bot
sys.path.append('../qt-meta')
from ut_bot_adaptation import UTBot

class VectorizedRenko:
    """Minimalistic vectorized Renko"""
    
    def __init__(self, brick_size=0.1, max_history=100):
        self.brick_size = brick_size
        self.max_history = max_history
        self.data = []
    
    def add_tick(self, timestamp, price):
        """Add tick and return new bricks count"""
        if not self.data:
            self.data.append({
                'timestamp': timestamp,
                'datetime': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                'open': price, 'high': price, 'low': price, 'close': price, 'direction': 'none'
            })
            return 0
        
        last_close = self.data[-1]['close']
        price_diff = price - last_close
        
        if abs(price_diff) >= self.brick_size:
            num_bricks = int(abs(price_diff) / self.brick_size)
            direction = 'up' if price_diff > 0 else 'down'
            
            for i in range(num_bricks):
                if direction == 'up':
                    brick_open = last_close
                    brick_close = last_close + self.brick_size
                    brick_high = brick_close
                    brick_low = brick_open
                else:
                    brick_open = last_close
                    brick_close = last_close - self.brick_size
                    brick_high = brick_open
                    brick_low = brick_close
                
                self.data.append({
                    'timestamp': timestamp,
                    'datetime': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                    'open': round(brick_open, 1),
                    'high': round(brick_high, 1),
                    'low': round(brick_low, 1),
                    'close': round(brick_close, 1),
                    'direction': direction
                })
                
                last_close = brick_close
            
            # Trim history
            if len(self.data) > self.max_history:
                self.data = self.data[-self.max_history:]
            
            return num_bricks
        
        return 0
    
    def get_dataframe(self):
        """Get DataFrame for UT Bot"""
        if len(self.data) < 10:
            return pd.DataFrame()
        
        df = pd.DataFrame(self.data)
        df['datetime'] = pd.to_datetime(df['datetime'])
        return df

class SignalProcessor:
    """Minimalistic signal processor"""
    
    def __init__(self):
        self.last_signal = None
        self.signal_count = 0
    
    def process_signal(self, signals_df, current_price):
        """Process UT Bot signals"""
        if len(signals_df) == 0:
            return None
        
        latest_idx = len(signals_df) - 1
        latest_buy = signals_df['buy'].iloc[latest_idx]
        latest_sell = signals_df['sell'].iloc[latest_idx]
        
        signal = {
            'price': current_price,
            'buy': bool(latest_buy),
            'sell': bool(latest_sell),
            'type': 'NONE'
        }
        
        if latest_buy and not latest_sell:
            signal['type'] = 'BUY'
        elif latest_sell and not latest_buy:
            signal['type'] = 'SELL'
        
        # Check if new signal
        if signal['type'] != 'NONE':
            if not self.last_signal or signal['type'] != self.last_signal['type']:
                self.signal_count += 1
                self.last_signal = signal
                return signal
        
        return None

def connect_mt5():
    """Connect to MT5"""
    if not mt5.initialize():
        return False
    
    if not mt5.login(40496559, "@Ripper25", "Deriv-Demo"):
        mt5.shutdown()
        return False
    
    return True

def find_symbol():
    """Find Step Index"""
    for symbol in ["Step Index", "STEP Index"]:
        if mt5.symbol_info(symbol):
            return symbol
    return None

def send_order(symbol, order_type):
    """Send FOK order with timing"""
    start_time = time.perf_counter()
    
    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        return None, 0
    
    price = tick.ask if order_type == "BUY" else tick.bid
    action = mt5.ORDER_TYPE_BUY if order_type == "BUY" else mt5.ORDER_TYPE_SELL
    
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": 0.10,
        "type": action,
        "price": price,
        "deviation": 20,
        "magic": 12345,
        "comment": f"Pipeline {order_type}",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK,
    }
    
    result = mt5.order_send(request)
    execution_time = (time.perf_counter() - start_time) * 1000
    
    if result.retcode == mt5.TRADE_RETCODE_DONE:
        return result, execution_time
    
    return None, execution_time

def main():
    """Full pipeline test"""
    print("Full Pipeline Performance Test")
    print("Target: <500ms data processing")
    print("-" * 40)
    
    # Connect
    if not connect_mt5():
        print("MT5 connection failed")
        return
    
    symbol = find_symbol()
    if not symbol:
        print("Symbol not found")
        return
    
    print(f"Connected to {symbol}")
    
    # Initialize components
    renko = VectorizedRenko(brick_size=0.1, max_history=100)
    ut_bot = UTBot(atr_period=1, sensitivity=1)
    signal_processor = SignalProcessor()
    
    # Performance tracking
    data_processing_times = []
    order_execution_times = []
    pipeline_times = []
    
    print("\nStarting full pipeline test...")
    print("Will execute order on first UT Bot signal")
    
    tick_count = 0
    brick_count = 0
    last_tick_time = None
    order_executed = False
    
    try:
        while not order_executed:
            # STEP 1: Get tick
            tick = mt5.symbol_info_tick(symbol)
            if not tick or last_tick_time == tick.time:
                time.sleep(0.1)
                continue
            
            # Start full pipeline timing
            pipeline_start = time.perf_counter()
            
            last_tick_time = tick.time
            tick_count += 1
            
            # STEP 2: Data processing start
            data_start = time.perf_counter()
            
            # Add to Renko
            new_bricks = renko.add_tick(tick.time, tick.bid)
            
            if new_bricks:
                brick_count += new_bricks
                print(f"Tick {tick_count}: {tick.bid:.5f} -> {new_bricks} brick(s), Total: {brick_count}")
                
                # Get Renko DataFrame
                renko_df = renko.get_dataframe()

                print(f"Renko DataFrame length: {len(renko_df)}")

                if len(renko_df) >= 5:  # Lowered from 10 to 5
                    print("Running UT Bot...")
                    # UT Bot signal generation
                    signals_df = ut_bot.run(renko_df)
                    print(f"UT Bot signals generated, length: {len(signals_df)}")

                    # Signal processing
                    signal = signal_processor.process_signal(signals_df, tick.bid)
                    print(f"Signal processed: {signal}")
                    
                    # Data processing end
                    data_time = (time.perf_counter() - data_start) * 1000
                    data_processing_times.append(data_time)
                    
                    if signal:
                        print(f"\nSIGNAL: {signal['type']} at {signal['price']:.5f}")
                        print(f"Data processing time: {data_time:.1f}ms")
                        
                        # STEP 3: Order execution
                        result, exec_time = send_order(symbol, signal['type'])
                        order_execution_times.append(exec_time)
                        
                        if result:
                            print(f"Order executed: Ticket {result.order}")
                            print(f"Order execution time: {exec_time:.1f}ms")
                            order_executed = True
                        else:
                            print(f"Order failed, execution time: {exec_time:.1f}ms")
                    
                    # Total pipeline time
                    pipeline_time = (time.perf_counter() - pipeline_start) * 1000
                    pipeline_times.append(pipeline_time)
                    
                    # Performance check
                    if data_time > 500:
                        print(f"WARNING: Data processing {data_time:.1f}ms > 500ms target")
                
                else:
                    # Not enough data for UT Bot
                    print(f"Not enough Renko data: {len(renko_df)} < 5")
                    data_time = (time.perf_counter() - data_start) * 1000
                    data_processing_times.append(data_time)
            
            else:
                # No new bricks
                if tick_count % 20 == 0:  # Show every 20 ticks
                    print(f"No new bricks at tick {tick_count}, price: {tick.bid:.5f}")
                data_time = (time.perf_counter() - data_start) * 1000
                data_processing_times.append(data_time)
            
            # Show progress
            if tick_count % 10 == 0:
                avg_data = sum(data_processing_times[-10:]) / min(10, len(data_processing_times))
                print(f"Processed {tick_count} ticks, avg data time: {avg_data:.1f}ms")
    
    except KeyboardInterrupt:
        print("\nTest stopped by user")
    
    # Performance summary
    print(f"\nFull Pipeline Performance Summary:")
    print(f"Ticks processed: {tick_count}")
    print(f"Renko bricks: {brick_count}")
    print(f"Signals generated: {signal_processor.signal_count}")
    
    if data_processing_times:
        avg_data = sum(data_processing_times) / len(data_processing_times)
        max_data = max(data_processing_times)
        under_500 = sum(1 for t in data_processing_times if t < 500) / len(data_processing_times) * 100
        
        print(f"\nData Processing Performance:")
        print(f"Average: {avg_data:.1f}ms")
        print(f"Maximum: {max_data:.1f}ms")
        print(f"Under 500ms: {under_500:.1f}%")
        print(f"Rating: {'EXCELLENT' if avg_data < 100 else 'GOOD' if avg_data < 250 else 'ACCEPTABLE' if avg_data < 500 else 'SLOW'}")
    
    if order_execution_times:
        avg_exec = sum(order_execution_times) / len(order_execution_times)
        print(f"\nOrder Execution:")
        print(f"Average: {avg_exec:.1f}ms")
    
    if pipeline_times:
        avg_pipeline = sum(pipeline_times) / len(pipeline_times)
        print(f"\nTotal Pipeline:")
        print(f"Average: {avg_pipeline:.1f}ms")
    
    mt5.shutdown()
    print("Test completed")

if __name__ == "__main__":
    main()
