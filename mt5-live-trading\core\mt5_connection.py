#!/usr/bin/env python3
"""
MT5 Connection Manager
Handle MetaTrader 5 connection, login, and basic operations

Account: ********
Server: Deriv-Demo  
Password: @Ripper25
Symbol: Step Index
"""

import MetaTrader5 as mt5
import time
from datetime import datetime
import logging

class MT5Connection:
    """Manage MT5 connection and operations"""
    
    def __init__(self, login=********, password="@Ripper25", server="Deriv-Demo"):
        self.login = login
        self.password = password
        self.server = server
        self.connected = False
        self.symbol = None
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def connect(self):
        """Connect and login to MT5"""
        try:
            # Initialize MT5
            if not mt5.initialize():
                error = mt5.last_error()
                self.logger.error(f"MT5 initialization failed: {error}")
                return False
            
            self.logger.info("MT5 initialized successfully")
            
            # Login to account
            if not mt5.login(self.login, self.password, self.server):
                error = mt5.last_error()
                self.logger.error(f"Login failed: {error}")
                mt5.shutdown()
                return False
            
            self.logger.info(f"Logged in to account {self.login} on {self.server}")
            
            # Get account info
            account_info = mt5.account_info()
            if account_info:
                self.logger.info(f"💰 Account balance: ${account_info.balance:.2f}")
                self.logger.info(f"📊 Account equity: ${account_info.equity:.2f}")
            
            self.connected = True
            return True
            
        except Exception as e:
            self.logger.error(f"Connection error: {e}")
            return False
    
    def find_step_index_symbol(self):
        """Find and verify Step Index symbol"""
        step_index_names = [
            "Step Index",
            "STEP Index", 
            "Step_Index",
            "STEP_Index"
        ]
        
        self.logger.info("Searching for Step Index symbol...")

        for symbol_name in step_index_names:
            symbol_info = mt5.symbol_info(symbol_name)
            if symbol_info is not None:
                self.symbol = symbol_name
                self.logger.info(f"Found Step Index as: {symbol_name}")

                # Log symbol details
                self.logger.info(f"Current bid: {symbol_info.bid:.5f}")
                self.logger.info(f"Current ask: {symbol_info.ask:.5f}")
                self.logger.info(f"Spread: {symbol_info.spread}")
                self.logger.info(f"Point value: {symbol_info.point}")

                return True

        self.logger.error("Step Index symbol not found!")
        return False
    
    def get_tick(self):
        """Get current tick data for Step Index"""
        if not self.connected or not self.symbol:
            return None
            
        try:
            tick = mt5.symbol_info_tick(self.symbol)
            if tick:
                return {
                    'time': tick.time,
                    'bid': tick.bid,
                    'ask': tick.ask,
                    'last': tick.last,
                    'volume': tick.volume
                }
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting tick: {e}")
            return None
    
    def get_account_info(self):
        """Get current account information"""
        if not self.connected:
            return None
            
        try:
            account_info = mt5.account_info()
            if account_info:
                return {
                    'balance': account_info.balance,
                    'equity': account_info.equity,
                    'margin': account_info.margin,
                    'free_margin': account_info.margin_free,
                    'profit': account_info.profit
                }
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting account info: {e}")
            return None
    
    def send_order(self, action, volume, price=None, sl=None, tp=None, comment="UT Bot"):
        """Send market order"""
        if not self.connected or not self.symbol:
            self.logger.error("Not connected or symbol not found")
            return None
        
        try:
            # Prepare order request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": self.symbol,
                "volume": volume,
                "type": action,  # mt5.ORDER_TYPE_BUY or mt5.ORDER_TYPE_SELL
                "deviation": 20,
                "magic": 12345,
                "comment": comment,
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            # Add price for market orders
            if action == mt5.ORDER_TYPE_BUY:
                tick = mt5.symbol_info_tick(self.symbol)
                request["price"] = tick.ask
            elif action == mt5.ORDER_TYPE_SELL:
                tick = mt5.symbol_info_tick(self.symbol)
                request["price"] = tick.bid
            
            # Add SL/TP if provided
            if sl:
                request["sl"] = sl
            if tp:
                request["tp"] = tp
            
            # Send order
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                self.logger.error(f"Order failed: {result.retcode} - {result.comment}")
                return None
            
            self.logger.info(f"✅ Order executed: {comment}")
            self.logger.info(f"📊 Ticket: {result.order}")
            self.logger.info(f"💰 Volume: {volume}")
            self.logger.info(f"💱 Price: {result.price:.5f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Order execution error: {e}")
            return None
    
    def get_positions(self):
        """Get current open positions"""
        if not self.connected:
            return []
            
        try:
            positions = mt5.positions_get(symbol=self.symbol)
            return list(positions) if positions else []
            
        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return []
    
    def close_position(self, ticket):
        """Close specific position"""
        if not self.connected:
            return False
            
        try:
            positions = mt5.positions_get(ticket=ticket)
            if not positions:
                self.logger.error(f"Position {ticket} not found")
                return False
            
            position = positions[0]
            
            # Prepare close request
            if position.type == mt5.POSITION_TYPE_BUY:
                order_type = mt5.ORDER_TYPE_SELL
                price = mt5.symbol_info_tick(self.symbol).bid
            else:
                order_type = mt5.ORDER_TYPE_BUY
                price = mt5.symbol_info_tick(self.symbol).ask
            
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": self.symbol,
                "volume": position.volume,
                "type": order_type,
                "position": ticket,
                "price": price,
                "deviation": 20,
                "magic": 12345,
                "comment": "Close by UT Bot",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                self.logger.error(f"Close failed: {result.retcode} - {result.comment}")
                return False
            
            self.logger.info(f"✅ Position {ticket} closed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error closing position: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from MT5"""
        if self.connected:
            mt5.shutdown()
            self.connected = False
            self.logger.info("🔌 Disconnected from MT5")
    
    def __del__(self):
        """Cleanup on destruction"""
        self.disconnect()

# Test the connection
if __name__ == "__main__":
    print("🔌 Testing MT5 Connection...")
    
    mt5_conn = MT5Connection()
    
    if mt5_conn.connect():
        if mt5_conn.find_step_index_symbol():
            # Test tick data
            tick = mt5_conn.get_tick()
            if tick:
                print(f"📊 Current tick: {tick}")
            
            # Test account info
            account = mt5_conn.get_account_info()
            if account:
                print(f"💰 Account: {account}")
        
        mt5_conn.disconnect()
        print("✅ MT5 Connection test completed!")
    else:
        print("❌ MT5 Connection test failed!")
