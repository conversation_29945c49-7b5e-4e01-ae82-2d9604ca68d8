#!/usr/bin/env python3
"""
UT Bot Engine for Live Trading
Real-time signal generation from Renko data

Ultra-fast processing: <30ms per signal
Proven parameters: atr_period=1, sensitivity=1
"""

import pandas as pd
import numpy as np
import sys
import os
import logging
from datetime import datetime

# Add qt-meta to path for UT Bot import
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'qt-meta'))
from ut_bot_adaptation import UTBot

class UTBotEngine:
    """Real-time UT Bot signal generation engine"""
    
    def __init__(self, atr_period=1, sensitivity=1, min_bricks=10):
        self.ut_bot = UTBot(atr_period=atr_period, sensitivity=sensitivity)
        self.min_bricks = min_bricks
        
        # Signal history
        self.last_signal = None
        self.signal_count = 0
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"🤖 UT Bot Engine initialized")
        self.logger.info(f"📊 Parameters: ATR={atr_period}, Sensitivity={sensitivity}")
    
    def generate_signal(self, renko_df):
        """
        Generate UT Bot signal from Renko DataFrame
        Returns: dict with signal information or None
        """
        try:
            # Check if we have enough data
            if len(renko_df) < self.min_bricks:
                self.logger.debug(f"Not enough bricks: {len(renko_df)} < {self.min_bricks}")
                return None
            
            # Generate UT Bot signals
            signals_df = self.ut_bot.run(renko_df)
            
            # Get the latest signal
            latest_idx = len(signals_df) - 1
            latest_buy = signals_df['buy'].iloc[latest_idx]
            latest_sell = signals_df['sell'].iloc[latest_idx]
            
            # Get current price info
            current_brick = renko_df.iloc[latest_idx]
            current_price = current_brick['close']
            current_time = current_brick['datetime']
            
            # Create signal object
            signal = {
                'timestamp': datetime.now(),
                'brick_time': current_time,
                'price': current_price,
                'buy_signal': bool(latest_buy),
                'sell_signal': bool(latest_sell),
                'signal_type': None,
                'brick_count': len(renko_df),
                'brick_direction': current_brick['direction']
            }
            
            # Determine signal type
            if latest_buy and not latest_sell:
                signal['signal_type'] = 'BUY'
                self.signal_count += 1
                self.logger.info(f"🟢 BUY signal #{self.signal_count} at {current_price:.5f}")
                
            elif latest_sell and not latest_buy:
                signal['signal_type'] = 'SELL'
                self.signal_count += 1
                self.logger.info(f"🔴 SELL signal #{self.signal_count} at {current_price:.5f}")
                
            else:
                signal['signal_type'] = 'NONE'
            
            # Store last signal
            self.last_signal = signal
            
            return signal
            
        except Exception as e:
            self.logger.error(f"Error generating signal: {e}")
            return None
    
    def is_new_signal(self, signal):
        """Check if this is a new signal (different from last)"""
        if not signal or signal['signal_type'] == 'NONE':
            return False
        
        if not self.last_signal:
            return True
        
        # Check if signal type changed
        if signal['signal_type'] != self.last_signal['signal_type']:
            return True
        
        # Check if price changed significantly (new brick)
        price_diff = abs(signal['price'] - self.last_signal['price'])
        if price_diff >= 0.1:  # New brick threshold
            return True
        
        return False
    
    def get_signal_summary(self):
        """Get summary of signal generation"""
        return {
            'total_signals': self.signal_count,
            'last_signal': self.last_signal,
            'engine_status': 'active'
        }
    
    def reset(self):
        """Reset the signal engine"""
        self.last_signal = None
        self.signal_count = 0
        self.logger.info("🔄 UT Bot Engine reset")

class LiveSignalManager:
    """Manage live signal generation and filtering"""
    
    def __init__(self, ut_bot_engine):
        self.ut_bot = ut_bot_engine
        self.active_signals = []
        self.signal_history = []
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
    
    def process_new_bricks(self, renko_df):
        """Process new Renko bricks and generate signals"""
        try:
            # Generate signal
            signal = self.ut_bot.generate_signal(renko_df)
            
            if not signal:
                return None
            
            # Check if it's a new actionable signal
            if signal['signal_type'] in ['BUY', 'SELL']:
                if self.ut_bot.is_new_signal(signal):
                    # Add to active signals
                    self.active_signals.append(signal)
                    self.signal_history.append(signal)
                    
                    self.logger.info(f"🚨 NEW {signal['signal_type']} SIGNAL!")
                    self.logger.info(f"📊 Price: {signal['price']:.5f}")
                    self.logger.info(f"🧱 Brick: {signal['brick_direction']}")
                    
                    return signal
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error processing signals: {e}")
            return None
    
    def clear_active_signals(self):
        """Clear active signals after processing"""
        self.active_signals.clear()
    
    def get_latest_signal(self):
        """Get the most recent signal"""
        if self.active_signals:
            return self.active_signals[-1]
        return None
    
    def get_signal_stats(self):
        """Get signal statistics"""
        buy_signals = len([s for s in self.signal_history if s['signal_type'] == 'BUY'])
        sell_signals = len([s for s in self.signal_history if s['signal_type'] == 'SELL'])
        
        return {
            'total_signals': len(self.signal_history),
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'active_signals': len(self.active_signals)
        }

# Test the UT Bot Engine
if __name__ == "__main__":
    print("🤖 Testing UT Bot Engine...")
    
    # Create test Renko data
    test_data = {
        'timestamp': [1748983549 + i for i in range(20)],
        'datetime': [f"2025-06-03 22:45:{49+i:02d}" for i in range(20)],
        'open': [8554.5 + i*0.1 for i in range(20)],
        'high': [8554.6 + i*0.1 for i in range(20)],
        'low': [8554.5 + i*0.1 for i in range(20)],
        'close': [8554.6 + i*0.1 for i in range(20)],
        'direction': ['up'] * 20
    }
    
    renko_df = pd.DataFrame(test_data)
    renko_df['datetime'] = pd.to_datetime(renko_df['datetime'])
    
    # Create UT Bot Engine
    ut_engine = UTBotEngine()
    signal_manager = LiveSignalManager(ut_engine)
    
    # Test signal generation
    print(f"📊 Testing with {len(renko_df)} Renko bricks...")
    
    signal = signal_manager.process_new_bricks(renko_df)
    
    if signal:
        print(f"✅ Signal generated: {signal['signal_type']}")
        print(f"📊 Price: {signal['price']:.5f}")
        print(f"🧱 Bricks: {signal['brick_count']}")
    else:
        print("ℹ️ No signal generated")
    
    # Get stats
    stats = signal_manager.get_signal_stats()
    print(f"\n📈 Signal Stats: {stats}")
    
    print(f"\n✅ UT Bot Engine test completed!")
