#!/usr/bin/env python3
"""
Live UT Bot Trading Strategy Engine
Real-time implementation of UT Bot with lightning-fast execution

Features:
- Real-time tick processing
- Live signal generation
- Dynamic risk management
- Position management
- Performance monitoring
- Error recovery
"""

import asyncio
import logging
import time
import threading
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
from collections import deque
import pandas as pd
import numpy as np

# Import our custom modules
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.websocket_collector import HighPerformanceTickCollector
from mt5.mt5_manager import MT5Manager, TradeRequest, TradeResult
from utils.renko_builder import RenkoBuilder
from utils.risk_manager import DynamicRiskManager

class LiveUTBot:
    def __init__(self, config: Dict):
        self.config = config
        
        # Core components
        self.tick_collector = None
        self.mt5_manager = None
        self.renko_builder = None
        self.risk_manager = None
        
        # Strategy parameters
        self.symbol = config.get('symbol', 'stpRNG')
        self.brick_size = config.get('brick_size', 0.1)
        self.atr_period = config.get('atr_period', 1)
        self.sensitivity = config.get('sensitivity', 1)
        
        # Trading state
        self.running = False
        self.current_position = None
        self.last_signal_time = 0
        self.signal_cooldown = config.get('signal_cooldown', 5)  # seconds
        
        # Performance tracking
        self.trades_executed = 0
        self.signals_generated = 0
        self.start_time = None
        
        # Data buffers
        self.tick_buffer = deque(maxlen=1000)
        self.renko_buffer = deque(maxlen=100)
        self.signal_history = deque(maxlen=50)
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Logging
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        
        # UT Bot state
        self.ut_bot_state = {
            'xATRTrailingStop_history': [],
            'pos_history': [],
            'true_range_history': [],
            'atr_history': []
        }
        
    async def initialize(self) -> bool:
        """Initialize all components"""
        try:
            self.logger.info("🚀 Initializing Live UT Bot...")
            
            # Initialize MT5 Manager
            self.mt5_manager = MT5Manager(
                login=self.config.get('mt5_login'),
                password=self.config.get('mt5_password'),
                server=self.config.get('mt5_server')
            )
            
            if not self.mt5_manager.initialize():
                raise Exception("Failed to initialize MT5 Manager")
                
            # Initialize Tick Collector
            self.tick_collector = HighPerformanceTickCollector(
                app_id=self.config.get('deriv_app_id', '71058'),
                symbols=[self.symbol],
                buffer_size=5000
            )
            
            # Add tick callback
            self.tick_collector.add_tick_callback(self.on_tick_received)
            
            # Initialize Renko Builder
            self.renko_builder = RenkoBuilder(
                brick_size=self.brick_size,
                buffer_size=200
            )
            
            # Initialize Risk Manager
            self.risk_manager = DynamicRiskManager(
                initial_balance=self.config.get('initial_balance', 10.0),
                max_risk_per_trade=self.config.get('max_risk_per_trade', 0.06),
                min_risk_per_trade=self.config.get('min_risk_per_trade', 0.01)
            )
            
            self.logger.info("✅ All components initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Initialization failed: {e}")
            return False
            
    async def start(self):
        """Start the live trading engine"""
        if not await self.initialize():
            raise Exception("Failed to initialize trading engine")
            
        self.running = True
        self.start_time = time.time()
        
        self.logger.info("🎯 Starting live trading...")
        
        # Start tick collection
        await self.tick_collector.start()
        
    async def stop(self):
        """Stop the trading engine"""
        self.logger.info("🛑 Stopping live trading...")
        self.running = False
        
        # Stop tick collector
        if self.tick_collector:
            await self.tick_collector.stop()
            
        # Close MT5 connection
        if self.mt5_manager:
            self.mt5_manager.shutdown()
            
        self.logger.info("✅ Live trading stopped")
        
    async def on_tick_received(self, tick: Dict):
        """Process incoming tick data"""
        try:
            if not self.running:
                return
                
            # Add to tick buffer
            with self.lock:
                self.tick_buffer.append(tick)
                
            # Update Renko chart
            new_brick = self.renko_builder.add_tick(tick['price'], tick['timestamp'])
            
            if new_brick:
                with self.lock:
                    self.renko_buffer.append(new_brick)
                    
                # Generate UT Bot signal
                await self.process_renko_update()
                
        except Exception as e:
            self.logger.error(f"❌ Tick processing error: {e}")
            
    async def process_renko_update(self):
        """Process new Renko brick and generate signals"""
        try:
            if len(self.renko_buffer) < 2:
                return
                
            # Convert Renko buffer to DataFrame for UT Bot
            renko_data = []
            for brick in list(self.renko_buffer)[-20:]:  # Use last 20 bricks
                renko_data.append({
                    'open': brick['open'],
                    'high': brick['high'],
                    'low': brick['low'],
                    'close': brick['close'],
                    'datetime': brick['datetime']
                })
                
            if len(renko_data) < 2:
                return
                
            df = pd.DataFrame(renko_data)
            
            # Run UT Bot calculation
            signal = self.calculate_ut_bot_signal(df)
            
            if signal:
                await self.handle_signal(signal)
                
        except Exception as e:
            self.logger.error(f"❌ Renko processing error: {e}")
            
    def calculate_ut_bot_signal(self, df: pd.DataFrame) -> Optional[Dict]:
        """Calculate UT Bot signal from Renko data"""
        try:
            if len(df) < 2:
                return None
                
            # Get latest data
            current = df.iloc[-1]
            previous = df.iloc[-2] if len(df) > 1 else current
            
            # Calculate True Range
            tr = max(
                current['high'] - current['low'],
                abs(current['high'] - previous['close']),
                abs(current['low'] - previous['close'])
            )
            
            self.ut_bot_state['true_range_history'].append(tr)
            
            # Calculate ATR
            if len(self.ut_bot_state['true_range_history']) >= self.atr_period:
                if len(self.ut_bot_state['atr_history']) == 0:
                    # Initial ATR
                    atr = np.mean(self.ut_bot_state['true_range_history'][-self.atr_period:])
                else:
                    # Wilder's smoothing
                    prev_atr = self.ut_bot_state['atr_history'][-1]
                    atr = (prev_atr * (self.atr_period - 1) + tr) / self.atr_period
                    
                self.ut_bot_state['atr_history'].append(atr)
                
                # Calculate trailing stop
                nLoss = self.sensitivity * atr
                src = current['close']
                
                prev_stop = self.ut_bot_state['xATRTrailingStop_history'][-1] if self.ut_bot_state['xATRTrailingStop_history'] else src
                prev_src = previous['close']
                
                # Calculate new trailing stop
                if src > prev_stop and prev_src > prev_stop:
                    new_stop = max(prev_stop, src - nLoss)
                elif src < prev_stop and prev_src < prev_stop:
                    new_stop = min(prev_stop, src + nLoss)
                elif src > prev_stop:
                    new_stop = src - nLoss
                else:
                    new_stop = src + nLoss
                    
                self.ut_bot_state['xATRTrailingStop_history'].append(new_stop)
                
                # Determine position
                prev_pos = self.ut_bot_state['pos_history'][-1] if self.ut_bot_state['pos_history'] else 0
                
                if prev_src < prev_stop and src > prev_stop:
                    new_pos = 1  # Long
                elif prev_src > prev_stop and src < prev_stop:
                    new_pos = -1  # Short
                else:
                    new_pos = prev_pos
                    
                self.ut_bot_state['pos_history'].append(new_pos)
                
                # Generate signal
                buy_signal = src > new_stop and new_pos == 1 and prev_pos != 1
                sell_signal = src < new_stop and new_pos == -1 and prev_pos != -1
                
                if buy_signal or sell_signal:
                    return {
                        'type': 'BUY' if buy_signal else 'SELL',
                        'price': src,
                        'stop_loss': new_stop,
                        'timestamp': current['datetime'],
                        'atr': atr,
                        'confidence': 1.0  # UT Bot signals are binary
                    }
                    
            return None
            
        except Exception as e:
            self.logger.error(f"❌ UT Bot calculation error: {e}")
            return None
            
    async def handle_signal(self, signal: Dict):
        """Handle trading signal"""
        try:
            current_time = time.time()
            
            # Check signal cooldown
            if current_time - self.last_signal_time < self.signal_cooldown:
                return
                
            self.last_signal_time = current_time
            
            with self.lock:
                self.signals_generated += 1
                self.signal_history.append(signal)
                
            self.logger.info(f"📊 Signal Generated: {signal['type']} @ {signal['price']}")
            
            # Check if we should execute trade
            if await self.should_execute_trade(signal):
                await self.execute_trade(signal)
                
        except Exception as e:
            self.logger.error(f"❌ Signal handling error: {e}")
            
    async def should_execute_trade(self, signal: Dict) -> bool:
        """Determine if trade should be executed"""
        try:
            # Check if MT5 is connected
            if not self.mt5_manager.connected:
                return False
                
            # Check current positions
            positions = self.mt5_manager.get_positions(self.symbol)
            
            # Simple logic: close opposite position and open new one
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Trade validation error: {e}")
            return False
            
    async def execute_trade(self, signal: Dict):
        """Execute trade based on signal"""
        try:
            # Close existing positions
            positions = self.mt5_manager.get_positions(self.symbol)
            for position in positions:
                close_result = self.mt5_manager.close_position(self.symbol, position['identifier'])
                if close_result.success:
                    self.logger.info(f"✅ Closed position: {position['identifier']}")
                    
            # Calculate position size
            account_info = self.mt5_manager.update_account_info()
            balance = account_info.get('balance', 1000)
            
            risk_amount = self.risk_manager.calculate_risk_amount(balance)
            stop_loss_pips = abs(signal['price'] - signal['stop_loss']) / 0.0001  # Convert to pips
            
            volume = self.mt5_manager.calculate_lot_size(
                self.symbol,
                risk_amount,
                stop_loss_pips
            )
            
            if volume > 0:
                # Create trade request
                trade_request = TradeRequest(
                    symbol=self.symbol,
                    action=signal['type'],
                    volume=volume,
                    sl=signal['stop_loss'],
                    comment=f"UT_Bot_{signal['type']}"
                )
                
                # Execute trade
                result = self.mt5_manager.send_order(trade_request)
                
                if result.success:
                    with self.lock:
                        self.trades_executed += 1
                        
                    self.logger.info(f"✅ Trade executed: {signal['type']} {volume} @ {result.price}")
                    
                    # Update risk manager
                    self.risk_manager.record_trade_outcome(0)  # Will be updated when trade closes
                    
                else:
                    self.logger.error(f"❌ Trade failed: {result.error_description}")
                    
        except Exception as e:
            self.logger.error(f"❌ Trade execution error: {e}")
            
    def get_performance_stats(self) -> Dict:
        """Get performance statistics"""
        uptime = time.time() - self.start_time if self.start_time else 0
        
        with self.lock:
            return {
                'uptime_seconds': uptime,
                'signals_generated': self.signals_generated,
                'trades_executed': self.trades_executed,
                'signal_rate': self.signals_generated / (uptime / 3600) if uptime > 0 else 0,  # per hour
                'trade_rate': self.trades_executed / (uptime / 3600) if uptime > 0 else 0,  # per hour
                'running': self.running,
                'tick_buffer_size': len(self.tick_buffer),
                'renko_buffer_size': len(self.renko_buffer),
                'ut_bot_state_size': len(self.ut_bot_state['atr_history'])
            }

# Example usage
if __name__ == "__main__":
    async def main():
        config = {
            'symbol': 'stpRNG',
            'brick_size': 0.1,
            'atr_period': 1,
            'sensitivity': 1,
            'deriv_app_id': '71058',
            'mt5_login': None,  # Add your MT5 credentials
            'mt5_password': None,
            'mt5_server': None,
            'initial_balance': 10.0,
            'max_risk_per_trade': 0.06,
            'signal_cooldown': 5
        }
        
        bot = LiveUTBot(config)
        
        try:
            await bot.start()
        except KeyboardInterrupt:
            await bot.stop()
            
    asyncio.run(main())
