#!/usr/bin/env python3
"""
Collect 500 Step Index Ticks and Save to CSV
Compare with existing stpRNG data format

Account: ********
Server: Deriv-Demo
Password: @Ripper25
"""

import MetaTrader5 as mt5
import time
import csv
from datetime import datetime

def collect_500_ticks():
    """Collect 500 ticks from Step Index and save to CSV"""
    print("📊 Collecting 500 Step Index Ticks")
    print("=" * 50)
    
    # Initialize MT5
    if not mt5.initialize():
        print(f"❌ MT5 initialization failed: {mt5.last_error()}")
        return False
    
    print("✅ MT5 initialized")
    
    # Login
    login = ********
    password = "@Ripper25"
    server = "Deriv-Demo"
    
    print(f"🔐 Logging in to {login} on {server}...")
    
    if not mt5.login(login, password, server):
        print(f"❌ Login failed: {mt5.last_error()}")
        mt5.shutdown()
        return False
    
    print("✅ Login successful")
    
    # Find Step Index symbol
    step_index_names = ["Step Index", "STEP Index", "Step_Index", "STEP_Index"]
    
    found_symbol = None
    for symbol_name in step_index_names:
        test_info = mt5.symbol_info(symbol_name)
        if test_info is not None:
            found_symbol = symbol_name
            print(f"✅ Found Step Index as: {symbol_name}")
            break
    
    if not found_symbol:
        print("❌ Step Index symbol not found!")
        mt5.shutdown()
        return False
    
    # Prepare CSV file
    csv_filename = "step_index_500_ticks.csv"
    
    print(f"\n📈 Collecting 500 ticks from {found_symbol}...")
    print(f"💾 Saving to: {csv_filename}")
    print("⏱️ This will take about 8-9 minutes (500 seconds)")
    print("-" * 60)
    
    tick_data = []
    tick_count = 0
    last_tick_time = None
    start_time = time.time()
    
    try:
        while tick_count < 500:
            tick = mt5.symbol_info_tick(found_symbol)
            
            if tick:
                # Check if this is a new tick (different timestamp)
                if last_tick_time != tick.time:
                    tick_count += 1
                    last_tick_time = tick.time
                    
                    # Store tick data
                    tick_record = {
                        'timestamp': tick.time,
                        'datetime': datetime.fromtimestamp(tick.time).strftime('%Y-%m-%d %H:%M:%S'),
                        'bid': tick.bid,
                        'ask': tick.ask,
                        'mid': (tick.bid + tick.ask) / 2,  # Calculate mid price
                        'spread': tick.ask - tick.bid
                    }
                    
                    tick_data.append(tick_record)
                    
                    # Show progress every 50 ticks
                    if tick_count % 50 == 0:
                        elapsed = time.time() - start_time
                        remaining = (500 - tick_count) * (elapsed / tick_count) if tick_count > 0 else 0
                        print(f"Progress: {tick_count}/500 ticks | "
                              f"Elapsed: {elapsed/60:.1f}min | "
                              f"ETA: {remaining/60:.1f}min | "
                              f"Price: {tick.bid:.5f}")
                    elif tick_count % 10 == 0:
                        print(f"Tick #{tick_count:3d} | "
                              f"Time: {datetime.fromtimestamp(tick.time).strftime('%H:%M:%S')} | "
                              f"Bid: {tick.bid:.5f} | Ask: {tick.ask:.5f}")
                        
            time.sleep(1.0)  # Wait 1 second for next tick
            
    except KeyboardInterrupt:
        print(f"\n🛑 Collection stopped by user at {tick_count} ticks")
    
    # Save to CSV
    print(f"\n💾 Saving {len(tick_data)} ticks to {csv_filename}...")
    
    with open(csv_filename, 'w', newline='') as csvfile:
        fieldnames = ['timestamp', 'datetime', 'bid', 'ask', 'mid', 'spread']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for tick_record in tick_data:
            writer.writerow(tick_record)
    
    # Show summary
    elapsed = time.time() - start_time
    
    print(f"\n📊 Collection Summary:")
    print(f"  Ticks collected: {len(tick_data)}")
    print(f"  Duration: {elapsed/60:.1f} minutes")
    print(f"  Average rate: {len(tick_data)/elapsed:.2f} ticks/second")
    print(f"  File saved: {csv_filename}")
    
    if len(tick_data) > 0:
        first_tick = tick_data[0]
        last_tick = tick_data[-1]
        price_range = max([t['bid'] for t in tick_data]) - min([t['bid'] for t in tick_data])
        
        print(f"\n📈 Price Analysis:")
        print(f"  First tick: {first_tick['datetime']} @ {first_tick['bid']:.5f}")
        print(f"  Last tick:  {last_tick['datetime']} @ {last_tick['bid']:.5f}")
        print(f"  Price range: {price_range:.5f}")
        print(f"  Avg spread: {sum([t['spread'] for t in tick_data])/len(tick_data):.5f}")
    
    # Cleanup
    mt5.shutdown()
    print("✅ Collection completed")
    
    return True

def main():
    """Main function"""
    try:
        success = collect_500_ticks()
        
        if success:
            print("\n🎯 500 TICK COLLECTION COMPLETED!")
            print("📁 Check step_index_500_ticks.csv")
            print("🔍 Compare with qt-meta/stpRNG_7days_renko_0_1.csv")
        else:
            print("\n❌ TICK COLLECTION FAILED!")
            
    except Exception as e:
        print(f"\n💥 Collection error: {e}")

if __name__ == "__main__":
    main()
