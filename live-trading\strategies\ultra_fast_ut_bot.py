#!/usr/bin/env python3
"""
Ultra-Fast UT Bot Implementation
Optimized for sub-500ms latency using JAX, Numba, and other high-performance libraries

Performance Targets:
- Signal calculation: <50ms
- Data processing: <100ms
- Total latency: <500ms
"""

import jax
import jax.numpy as jnp
from jax import jit, vmap
import numba
from numba import njit, float64, int32
import numpy as np
import polars as pl
import time
import logging
from typing import Dict, List, Optional, Tuple
from collections import deque
import threading

# Configure JAX for optimal performance
jax.config.update("jax_enable_x64", True)  # Use 64-bit precision
jax.config.update("jax_platform_name", "cpu")  # Use CPU (change to "gpu" if available)

class UltraFastUTBot:
    """Ultra-optimized UT Bot using JAX and Numba for maximum speed"""
    
    def __init__(self, atr_period: int = 1, sensitivity: float = 1.0, buffer_size: int = 1000):
        self.atr_period = atr_period
        self.sensitivity = sensitivity
        self.buffer_size = buffer_size
        
        # High-performance data buffers using JAX arrays
        self.price_buffer = deque(maxlen=buffer_size)
        self.high_buffer = deque(maxlen=buffer_size)
        self.low_buffer = deque(maxlen=buffer_size)
        self.close_buffer = deque(maxlen=buffer_size)
        
        # Pre-compiled JAX functions
        self.jax_calculate_atr = jit(self._jax_calculate_atr_impl)
        self.jax_calculate_trailing_stop = jit(self._jax_calculate_trailing_stop_impl)
        self.jax_generate_signals = jit(self._jax_generate_signals_impl)
        
        # Performance tracking
        self.calculation_times = deque(maxlen=100)
        self.total_calculations = 0
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Logging
        self.logger = logging.getLogger(__name__)
        
    @staticmethod
    @jit
    def _jax_calculate_atr_impl(highs: jnp.ndarray, lows: jnp.ndarray, 
                               closes: jnp.ndarray, period: int) -> jnp.ndarray:
        """JAX-optimized ATR calculation"""
        # Calculate True Range
        prev_closes = jnp.concatenate([jnp.array([closes[0]]), closes[:-1]])
        
        range1 = highs - lows
        range2 = jnp.abs(highs - prev_closes)
        range3 = jnp.abs(lows - prev_closes)
        
        true_ranges = jnp.maximum(jnp.maximum(range1, range2), range3)
        
        # Calculate ATR using Wilder's smoothing
        def atr_step(carry, tr):
            prev_atr = carry
            new_atr = jnp.where(
                prev_atr == 0,
                jnp.mean(true_ranges[:period]),  # Initial ATR
                (prev_atr * (period - 1) + tr) / period  # Wilder's smoothing
            )
            return new_atr, new_atr
        
        _, atrs = jax.lax.scan(atr_step, 0.0, true_ranges)
        return atrs
    
    @staticmethod
    @jit
    def _jax_calculate_trailing_stop_impl(closes: jnp.ndarray, atrs: jnp.ndarray, 
                                         sensitivity: float) -> jnp.ndarray:
        """JAX-optimized trailing stop calculation"""
        n_loss = sensitivity * atrs
        
        def trailing_stop_step(carry, inputs):
            prev_stop = carry
            close, loss = inputs
            prev_close = jnp.where(carry == 0, close, 
                                 jnp.concatenate([jnp.array([close]), closes[:-1]])[0])
            
            # Calculate new trailing stop
            new_stop = jnp.where(
                (close > prev_stop) & (prev_close > prev_stop),
                jnp.maximum(prev_stop, close - loss),
                jnp.where(
                    (close < prev_stop) & (prev_close < prev_stop),
                    jnp.minimum(prev_stop, close + loss),
                    jnp.where(
                        close > prev_stop,
                        close - loss,
                        close + loss
                    )
                )
            )
            
            return new_stop, new_stop
        
        _, trailing_stops = jax.lax.scan(
            trailing_stop_step, 
            closes[0], 
            (closes, n_loss)
        )
        
        return trailing_stops
    
    @staticmethod
    @jit
    def _jax_generate_signals_impl(closes: jnp.ndarray, 
                                  trailing_stops: jnp.ndarray) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """JAX-optimized signal generation"""
        # Calculate position changes
        prev_closes = jnp.concatenate([jnp.array([closes[0]]), closes[:-1]])
        prev_stops = jnp.concatenate([jnp.array([trailing_stops[0]]), trailing_stops[:-1]])
        
        # Buy signals: price crosses above trailing stop
        buy_signals = (prev_closes <= prev_stops) & (closes > trailing_stops)
        
        # Sell signals: price crosses below trailing stop
        sell_signals = (prev_closes >= prev_stops) & (closes < trailing_stops)
        
        return buy_signals, sell_signals
    
    @staticmethod
    @njit(float64[:](float64[:], float64[:], float64[:], int32), cache=True)
    def _numba_calculate_atr(highs, lows, closes, period):
        """Numba-optimized ATR calculation for fallback"""
        n = len(closes)
        atrs = np.zeros(n)
        true_ranges = np.zeros(n)
        
        # Calculate True Ranges
        for i in range(n):
            if i == 0:
                true_ranges[i] = highs[i] - lows[i]
            else:
                range1 = highs[i] - lows[i]
                range2 = abs(highs[i] - closes[i-1])
                range3 = abs(lows[i] - closes[i-1])
                true_ranges[i] = max(range1, range2, range3)
        
        # Calculate ATR
        for i in range(n):
            if i < period - 1:
                atrs[i] = 0.0
            elif i == period - 1:
                atrs[i] = np.mean(true_ranges[:period])
            else:
                atrs[i] = (atrs[i-1] * (period - 1) + true_ranges[i]) / period
                
        return atrs
    
    def add_tick(self, price: float, high: float = None, low: float = None) -> Optional[Dict]:
        """Add new tick data and return signal if generated"""
        start_time = time.perf_counter()
        
        # Use price as OHLC if not provided (for tick data)
        if high is None:
            high = price
        if low is None:
            low = price
            
        with self.lock:
            # Add to buffers
            self.price_buffer.append(price)
            self.high_buffer.append(high)
            self.low_buffer.append(low)
            self.close_buffer.append(price)
            
            # Need at least 2 data points for calculation
            if len(self.close_buffer) < 2:
                return None
                
            # Convert to JAX arrays for ultra-fast computation
            try:
                signal = self._calculate_signal_jax()
            except Exception as e:
                # Fallback to Numba if JAX fails
                self.logger.warning(f"JAX calculation failed, using Numba fallback: {e}")
                signal = self._calculate_signal_numba()
                
            # Track performance
            calculation_time = (time.perf_counter() - start_time) * 1000  # Convert to ms
            self.calculation_times.append(calculation_time)
            self.total_calculations += 1
            
            if calculation_time > 100:  # Log if calculation takes >100ms
                self.logger.warning(f"Slow calculation: {calculation_time:.2f}ms")
                
            return signal
    
    def _calculate_signal_jax(self) -> Optional[Dict]:
        """Calculate signal using JAX (fastest method)"""
        # Convert buffers to JAX arrays
        highs = jnp.array(list(self.high_buffer))
        lows = jnp.array(list(self.low_buffer))
        closes = jnp.array(list(self.close_buffer))
        
        # Calculate ATR
        atrs = self.jax_calculate_atr(highs, lows, closes, self.atr_period)
        
        # Calculate trailing stops
        trailing_stops = self.jax_calculate_trailing_stop(closes, atrs, self.sensitivity)
        
        # Generate signals
        buy_signals, sell_signals = self.jax_generate_signals(closes, trailing_stops)
        
        # Check if we have a new signal (last element)
        latest_buy = bool(buy_signals[-1])
        latest_sell = bool(sell_signals[-1])
        
        if latest_buy or latest_sell:
            return {
                'type': 'BUY' if latest_buy else 'SELL',
                'price': float(closes[-1]),
                'trailing_stop': float(trailing_stops[-1]),
                'atr': float(atrs[-1]),
                'timestamp': time.time(),
                'confidence': 1.0,
                'calculation_method': 'JAX'
            }
            
        return None
    
    def _calculate_signal_numba(self) -> Optional[Dict]:
        """Calculate signal using Numba (fallback method)"""
        # Convert buffers to NumPy arrays
        highs = np.array(list(self.high_buffer), dtype=np.float64)
        lows = np.array(list(self.low_buffer), dtype=np.float64)
        closes = np.array(list(self.close_buffer), dtype=np.float64)
        
        # Calculate ATR using Numba
        atrs = self._numba_calculate_atr(highs, lows, closes, self.atr_period)
        
        # Simple signal logic for fallback
        if len(closes) >= 2 and atrs[-1] > 0:
            current_price = closes[-1]
            prev_price = closes[-2]
            atr = atrs[-1]
            
            # Simple crossover logic
            trailing_stop = current_price - (self.sensitivity * atr)
            
            if prev_price <= trailing_stop and current_price > trailing_stop:
                return {
                    'type': 'BUY',
                    'price': current_price,
                    'trailing_stop': trailing_stop,
                    'atr': atr,
                    'timestamp': time.time(),
                    'confidence': 1.0,
                    'calculation_method': 'Numba'
                }
            elif prev_price >= trailing_stop and current_price < trailing_stop:
                return {
                    'type': 'SELL',
                    'price': current_price,
                    'trailing_stop': trailing_stop,
                    'atr': atr,
                    'timestamp': time.time(),
                    'confidence': 1.0,
                    'calculation_method': 'Numba'
                }
                
        return None
    
    def get_performance_stats(self) -> Dict:
        """Get performance statistics"""
        with self.lock:
            if not self.calculation_times:
                return {
                    'total_calculations': 0,
                    'average_time_ms': 0,
                    'min_time_ms': 0,
                    'max_time_ms': 0,
                    'recent_average_ms': 0
                }
                
            times = list(self.calculation_times)
            recent_times = times[-10:] if len(times) >= 10 else times
            
            return {
                'total_calculations': self.total_calculations,
                'average_time_ms': np.mean(times),
                'min_time_ms': np.min(times),
                'max_time_ms': np.max(times),
                'recent_average_ms': np.mean(recent_times),
                'buffer_size': len(self.close_buffer),
                'target_latency_ms': 50,
                'performance_ratio': 50 / np.mean(recent_times) if recent_times else 0
            }
    
    def reset(self):
        """Reset all buffers and state"""
        with self.lock:
            self.price_buffer.clear()
            self.high_buffer.clear()
            self.low_buffer.clear()
            self.close_buffer.clear()
            self.calculation_times.clear()
            self.total_calculations = 0

# Performance testing and benchmarking
class UltraFastPerformanceTester:
    """Test and benchmark the ultra-fast UT Bot"""
    
    @staticmethod
    def benchmark_calculation_speed(num_iterations: int = 1000) -> Dict:
        """Benchmark calculation speed"""
        bot = UltraFastUTBot(atr_period=1, sensitivity=1.0)
        
        # Generate test data
        np.random.seed(42)
        base_price = 100.0
        prices = base_price + np.cumsum(np.random.randn(num_iterations) * 0.01)
        
        start_time = time.perf_counter()
        signals_generated = 0
        
        for price in prices:
            signal = bot.add_tick(price)
            if signal:
                signals_generated += 1
                
        total_time = (time.perf_counter() - start_time) * 1000  # Convert to ms
        
        stats = bot.get_performance_stats()
        
        return {
            'total_time_ms': total_time,
            'iterations': num_iterations,
            'signals_generated': signals_generated,
            'avg_time_per_tick_ms': total_time / num_iterations,
            'performance_stats': stats,
            'meets_target': stats['recent_average_ms'] < 50
        }

# Example usage and testing
if __name__ == "__main__":
    print("🚀 Testing Ultra-Fast UT Bot...")
    
    # Performance benchmark
    results = UltraFastPerformanceTester.benchmark_calculation_speed(1000)
    
    print(f"📊 Performance Results:")
    print(f"  Total time: {results['total_time_ms']:.2f}ms")
    print(f"  Average per tick: {results['avg_time_per_tick_ms']:.3f}ms")
    print(f"  Signals generated: {results['signals_generated']}")
    print(f"  Meets <50ms target: {'✅' if results['meets_target'] else '❌'}")
    
    # Real-time test
    print(f"\n⚡ Real-time test:")
    bot = UltraFastUTBot()
    
    for i in range(10):
        price = 100 + i * 0.1
        start = time.perf_counter()
        signal = bot.add_tick(price)
        elapsed = (time.perf_counter() - start) * 1000
        
        if signal:
            print(f"  Signal: {signal['type']} @ {signal['price']:.2f} ({elapsed:.2f}ms)")
        else:
            print(f"  Tick {i}: {price:.2f} ({elapsed:.2f}ms)")
            
    print("✅ Ultra-Fast UT Bot test completed!")
