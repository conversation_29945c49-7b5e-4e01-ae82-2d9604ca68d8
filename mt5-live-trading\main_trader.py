#!/usr/bin/env python3
"""
LIVE UT BOT TRADING SYSTEM
Real money trading with Step Index on MT5

⚠️  WARNING: THIS TRADES REAL MONEY ⚠️
- No simulations or paper trading
- Direct order execution on live account
- <500ms processing requirement
- Proven UT Bot strategy (99.2% trend capture)

Account: ******** (Deriv-Demo)
Symbol: Step Index
Strategy: Original UT Bot (ATR=1, Sensitivity=1)
"""

import sys
import os
import time
import logging
from datetime import datetime
import signal
import threading

# Add core modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))

# Import our modules
from mt5_connection import MT5Connection
from renko_builder import RealTimeRenkoBuilder
from ut_bot_engine import UTBotEngine, LiveSignalManager
from order_manager import OrderManager
from trading_config import *

class LiveUTBotTrader:
    """Main live trading system orchestrator"""
    
    def __init__(self):
        # System components
        self.mt5 = None
        self.renko = None
        self.ut_bot = None
        self.signal_manager = None
        self.order_manager = None
        
        # Trading state
        self.running = False
        self.last_tick_time = None
        self.tick_count = 0
        self.signal_count = 0
        
        # Performance tracking
        self.start_time = None
        self.processing_times = []
        
        # Setup logging
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def setup_logging(self):
        """Setup comprehensive logging"""
        # Create logs directory
        os.makedirs(FILE_PATHS['logs_dir'], exist_ok=True)
        
        # Configure logging
        log_format = '%(asctime)s | %(levelname)s | %(name)s | %(message)s'
        logging.basicConfig(
            level=getattr(logging, PERFORMANCE_CONFIG['log_level']),
            format=log_format,
            handlers=[
                logging.FileHandler(f"{FILE_PATHS['logs_dir']}/live_trading.log"),
                logging.StreamHandler()
            ]
        )
    
    def initialize_system(self):
        """Initialize all trading system components"""
        try:
            self.logger.info("🚀 INITIALIZING LIVE UT BOT TRADING SYSTEM")
            self.logger.info("=" * 60)
            
            # Validate configuration
            config_errors = validate_config()
            if config_errors:
                self.logger.error("❌ Configuration errors:")
                for error in config_errors:
                    self.logger.error(f"   • {error}")
                return False
            
            self.logger.info("✅ Configuration validated")
            
            # Show configuration summary
            config_summary = get_config_summary()
            for key, value in config_summary.items():
                self.logger.info(f"📊 {key}: {value}")
            
            # Initialize MT5 connection
            self.logger.info("\n🔌 Connecting to MT5...")
            self.mt5 = MT5Connection(
                login=MT5_CONFIG['login'],
                password=MT5_CONFIG['password'],
                server=MT5_CONFIG['server']
            )
            
            if not self.mt5.connect():
                self.logger.error("❌ Failed to connect to MT5")
                return False
            
            if not self.mt5.find_step_index_symbol():
                self.logger.error("❌ Failed to find Step Index symbol")
                return False
            
            # Initialize Renko builder
            self.logger.info("🧱 Initializing Renko builder...")
            self.renko = RealTimeRenkoBuilder(
                brick_size=RENKO_CONFIG['brick_size'],
                max_history=RENKO_CONFIG['max_history']
            )
            
            # Initialize UT Bot engine
            self.logger.info("🤖 Initializing UT Bot engine...")
            self.ut_bot = UTBotEngine(
                atr_period=UTBOT_CONFIG['atr_period'],
                sensitivity=UTBOT_CONFIG['sensitivity'],
                min_bricks=UTBOT_CONFIG['min_bricks']
            )
            
            self.signal_manager = LiveSignalManager(self.ut_bot)
            
            # Initialize order manager
            self.logger.info("💼 Initializing order manager...")
            self.order_manager = OrderManager(
                mt5_connection=self.mt5,
                initial_lot_size=RISK_CONFIG['initial_lot_size'],
                max_lot_size=RISK_CONFIG['max_lot_size']
            )
            
            self.logger.info("✅ ALL SYSTEMS INITIALIZED")
            self.logger.info("🚨 READY FOR LIVE TRADING!")
            self.logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            self.logger.error(f"💥 System initialization failed: {e}")
            return False
    
    def process_tick(self):
        """Process single tick - core trading loop"""
        try:
            start_time = time.time()
            
            # Get new tick
            tick = self.mt5.get_tick()
            if not tick:
                return False
            
            # Check if this is a new tick
            if self.last_tick_time == tick['time']:
                return False  # Same tick, skip
            
            self.last_tick_time = tick['time']
            self.tick_count += 1
            
            # Use bid price for Renko (consistent with backtests)
            price = tick['bid']
            timestamp = tick['time']
            
            # Add tick to Renko builder
            new_bricks = self.renko.add_tick(timestamp, price)
            
            if new_bricks:
                self.logger.info(f"🧱 New brick(s): {len(new_bricks)} at {price:.5f}")
                
                # Get current Renko data
                renko_df = self.renko.get_renko_dataframe(last_n=100)  # Last 100 bricks
                
                if len(renko_df) >= UTBOT_CONFIG['min_bricks']:
                    # Generate UT Bot signal
                    signal = self.signal_manager.process_new_bricks(renko_df)
                    
                    if signal:
                        self.signal_count += 1
                        self.logger.info(f"🚨 SIGNAL #{self.signal_count}: {signal['signal_type']}")
                        
                        # Check risk limits
                        if self.order_manager.check_risk_limits():
                            # Execute trade
                            success = self.order_manager.process_signal(signal)
                            
                            if success:
                                self.logger.info("✅ Signal processed successfully")
                            else:
                                self.logger.error("❌ Failed to process signal")
                        else:
                            self.logger.warning("⚠️ Risk limits exceeded - skipping trade")
            
            # Track processing time
            processing_time = (time.time() - start_time) * 1000  # ms
            self.processing_times.append(processing_time)
            
            # Check performance requirement
            if processing_time > SYSTEM_CONFIG['tick_processing_timeout']:
                self.logger.warning(f"⚠️ Slow processing: {processing_time:.1f}ms > {SYSTEM_CONFIG['tick_processing_timeout']}ms")
            
            # Log periodic stats
            if self.tick_count % 60 == 0:  # Every 60 ticks (1 minute)
                self.log_performance_stats()
            
            return True
            
        except Exception as e:
            self.logger.error(f"💥 Error processing tick: {e}")
            return False
    
    def log_performance_stats(self):
        """Log performance statistics"""
        try:
            # Calculate average processing time
            if self.processing_times:
                avg_processing = sum(self.processing_times[-60:]) / min(60, len(self.processing_times))
                max_processing = max(self.processing_times[-60:])
            else:
                avg_processing = 0
                max_processing = 0
            
            # Get trading summary
            trading_summary = self.order_manager.get_trading_summary()
            
            # Get account info
            account_info = self.mt5.get_account_info()
            
            self.logger.info("📊 PERFORMANCE STATS")
            self.logger.info(f"   Ticks processed: {self.tick_count}")
            self.logger.info(f"   Signals generated: {self.signal_count}")
            self.logger.info(f"   Orders executed: {trading_summary.get('orders_executed', 0)}")
            self.logger.info(f"   Avg processing: {avg_processing:.1f}ms")
            self.logger.info(f"   Max processing: {max_processing:.1f}ms")
            
            if account_info:
                self.logger.info(f"   Account balance: ${account_info['balance']:.2f}")
                self.logger.info(f"   Current profit: ${account_info['profit']:.2f}")
            
        except Exception as e:
            self.logger.error(f"Error logging stats: {e}")
    
    def run(self):
        """Main trading loop"""
        try:
            self.logger.info("🚀 STARTING LIVE TRADING")
            self.logger.info("⚠️  TRADING REAL MONEY - BE CAREFUL!")
            self.logger.info("Press Ctrl+C to stop trading")
            self.logger.info("-" * 60)
            
            self.running = True
            self.start_time = time.time()
            
            while self.running:
                # Process tick
                self.process_tick()
                
                # Small delay to prevent excessive CPU usage
                time.sleep(0.1)  # 100ms delay
            
        except KeyboardInterrupt:
            self.logger.info("🛑 Trading stopped by user")
        except Exception as e:
            self.logger.error(f"💥 Trading loop error: {e}")
        finally:
            self.shutdown()
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"🛑 Received signal {signum} - shutting down...")
        self.running = False
    
    def shutdown(self):
        """Graceful shutdown"""
        try:
            self.logger.info("🔄 Shutting down trading system...")
            
            # Log final stats
            self.log_performance_stats()
            
            # Save data if configured
            if PERFORMANCE_CONFIG['save_renko_to_csv'] and self.renko:
                self.renko.save_to_csv(FILE_PATHS['renko_file'])
            
            # Disconnect from MT5
            if self.mt5:
                self.mt5.disconnect()
            
            self.logger.info("✅ Shutdown completed")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")

def main():
    """Main entry point"""
    print("🚀 LIVE UT BOT TRADING SYSTEM")
    print("⚠️  WARNING: THIS TRADES REAL MONEY!")
    print("=" * 50)
    
    # Create and run trader
    trader = LiveUTBotTrader()
    
    if trader.initialize_system():
        trader.run()
    else:
        print("❌ Failed to initialize trading system")

if __name__ == "__main__":
    main()
