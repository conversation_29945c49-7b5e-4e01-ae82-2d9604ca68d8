#!/usr/bin/env python3
"""
Ultra-Fast Trading System Setup Script
Installs all required dependencies and optimizes the environment

Run this script first before launching the trading system.
"""

import subprocess
import sys
import os
import platform
import logging

def setup_logging():
    """Setup logging for the setup process"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def run_command(command, description):
    """Run a command and handle errors"""
    logger = logging.getLogger(__name__)
    logger.info(f"🔧 {description}...")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    logger = logging.getLogger(__name__)
    
    if sys.version_info < (3, 8):
        logger.error("❌ Python 3.8+ is required for optimal performance")
        return False
    
    logger.info(f"✅ Python {sys.version} is compatible")
    return True

def install_system_dependencies():
    """Install system-level dependencies"""
    logger = logging.getLogger(__name__)
    system = platform.system().lower()
    
    if system == "linux":
        # Ubuntu/Debian
        commands = [
            "sudo apt-get update",
            "sudo apt-get install -y build-essential",
            "sudo apt-get install -y python3-dev",
            "sudo apt-get install -y libblas-dev liblapack-dev",
            "sudo apt-get install -y gfortran"
        ]
        
        for cmd in commands:
            if not run_command(cmd, f"Installing system dependencies: {cmd}"):
                logger.warning(f"⚠️ Failed to run: {cmd}")
                
    elif system == "darwin":
        # macOS
        commands = [
            "brew install gcc",
            "brew install openblas",
            "brew install lapack"
        ]
        
        for cmd in commands:
            if not run_command(cmd, f"Installing macOS dependencies: {cmd}"):
                logger.warning(f"⚠️ Failed to run: {cmd}")
                
    elif system == "windows":
        logger.info("🪟 Windows detected - using pip for all dependencies")
    
    return True

def install_python_dependencies():
    """Install Python dependencies for ultra-fast performance"""
    logger = logging.getLogger(__name__)
    
    # Core performance libraries
    core_packages = [
        "jax[cpu]==0.4.23",
        "jaxlib==0.4.23", 
        "numba==0.58.1",
        "polars==0.20.3",
        "uvloop==0.19.0",
        "orjson==3.9.10",
        "websockets==12.0",
        "numpy==1.24.3",
        "scipy==1.11.4"
    ]
    
    # Trading specific
    trading_packages = [
        "MetaTrader5==5.0.45",
        "pandas==2.1.4",  # For compatibility
        "matplotlib==3.7.2",
        "psutil==5.9.6"
    ]
    
    # Development and monitoring
    dev_packages = [
        "structlog==23.2.0",
        "pytest==7.4.3",
        "pytest-asyncio==0.21.1",
        "line-profiler==4.1.1",
        "memory-profiler==0.61.0"
    ]
    
    all_packages = core_packages + trading_packages + dev_packages
    
    # Install packages
    for package in all_packages:
        if not run_command(f"pip install {package}", f"Installing {package}"):
            logger.warning(f"⚠️ Failed to install {package}")
    
    return True

def install_optional_gpu_support():
    """Install optional GPU support (CuPy)"""
    logger = logging.getLogger(__name__)
    
    try:
        # Try to detect CUDA
        result = subprocess.run("nvidia-smi", shell=True, capture_output=True)
        if result.returncode == 0:
            logger.info("🎮 NVIDIA GPU detected, installing CuPy...")
            return run_command("pip install cupy-cuda12x==12.3.0", "Installing CuPy for GPU acceleration")
        else:
            logger.info("ℹ️ No NVIDIA GPU detected, skipping CuPy installation")
            return True
    except:
        logger.info("ℹ️ CUDA not available, skipping GPU support")
        return True

def optimize_environment():
    """Optimize environment for performance"""
    logger = logging.getLogger(__name__)
    
    # Set environment variables for optimal performance
    env_vars = {
        "NUMBA_CACHE_DIR": "/tmp/numba_cache",
        "NUMBA_NUM_THREADS": "4",
        "OMP_NUM_THREADS": "4",
        "OPENBLAS_NUM_THREADS": "4",
        "MKL_NUM_THREADS": "4",
        "JAX_PLATFORM_NAME": "cpu",
        "JAX_ENABLE_X64": "True"
    }
    
    for var, value in env_vars.items():
        os.environ[var] = value
        logger.info(f"🔧 Set {var}={value}")
    
    return True

def test_installation():
    """Test that all critical libraries are working"""
    logger = logging.getLogger(__name__)
    
    tests = [
        ("import jax; import jax.numpy as jnp", "JAX"),
        ("import numba; from numba import jit", "Numba"),
        ("import polars as pl", "Polars"),
        ("import uvloop", "uvloop"),
        ("import orjson", "orjson"),
        ("import websockets", "websockets"),
        ("import MetaTrader5 as mt5", "MetaTrader5"),
        ("import numpy as np", "NumPy"),
        ("import asyncio", "asyncio")
    ]
    
    all_passed = True
    
    for test_code, library in tests:
        try:
            exec(test_code)
            logger.info(f"✅ {library} import successful")
        except ImportError as e:
            logger.error(f"❌ {library} import failed: {e}")
            all_passed = False
        except Exception as e:
            logger.error(f"❌ {library} test failed: {e}")
            all_passed = False
    
    return all_passed

def create_directories():
    """Create necessary directories"""
    logger = logging.getLogger(__name__)
    
    directories = [
        "logs",
        "data",
        "cache",
        "backups"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"📁 Created directory: {directory}")
    
    return True

def main():
    """Main setup function"""
    logger = setup_logging()
    
    logger.info("🚀 Ultra-Fast Trading System Setup")
    logger.info("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install system dependencies
    logger.info("📦 Installing system dependencies...")
    install_system_dependencies()
    
    # Install Python dependencies
    logger.info("🐍 Installing Python dependencies...")
    if not install_python_dependencies():
        logger.error("❌ Failed to install Python dependencies")
        sys.exit(1)
    
    # Install optional GPU support
    logger.info("🎮 Checking for GPU support...")
    install_optional_gpu_support()
    
    # Optimize environment
    logger.info("⚡ Optimizing environment...")
    optimize_environment()
    
    # Test installation
    logger.info("🧪 Testing installation...")
    if not test_installation():
        logger.error("❌ Installation test failed")
        sys.exit(1)
    
    logger.info("=" * 50)
    logger.info("✅ Ultra-Fast Trading System setup completed successfully!")
    logger.info("")
    logger.info("🎯 Next steps:")
    logger.info("1. Run: python launch_ultra_fast_trading.py")
    logger.info("2. Monitor performance in logs/")
    logger.info("3. Check MT5 connection and trades")
    logger.info("")
    logger.info("📊 Performance targets:")
    logger.info("- Total latency: <500ms")
    logger.info("- Signal calculation: <50ms") 
    logger.info("- Tick processing: <10ms")
    logger.info("- Order execution: <200ms")
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
