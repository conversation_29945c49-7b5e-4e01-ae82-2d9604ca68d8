#!/usr/bin/env python3
"""
Ultra-Fast UT Bot Engine
Optimized for <10ms signal generation using:
- Pure NumPy vectorization
- Numba JIT compilation
- No pandas overhead
- Pre-compiled mathematical functions
"""

import numpy as np
import numba as nb
from numba import jit, njit
import time
import logging
from collections import deque

# Pre-compile UT Bot calculations with <PERSON><PERSON>
@njit(cache=True)
def calculate_hl2(highs, lows):
    """Vectorized HL2 calculation"""
    return (highs + lows) / 2.0

@njit(cache=True)
def calculate_true_range(highs, lows, closes):
    """Vectorized True Range calculation"""
    n = len(closes)
    tr = np.zeros(n-1, dtype=np.float64)
    
    for i in range(1, n):
        tr[i-1] = max(
            highs[i] - lows[i],
            abs(highs[i] - closes[i-1]),
            abs(lows[i] - closes[i-1])
        )
    
    return tr

@njit(cache=True)
def calculate_rma(values, period):
    """Vectorized RMA (Running Moving Average) calculation"""
    n = len(values)
    if n < period:
        return np.mean(values)
    
    # Initialize with SMA
    alpha = 1.0 / period
    rma = np.zeros(n, dtype=np.float64)
    rma[0] = values[0]
    
    for i in range(1, n):
        rma[i] = alpha * values[i] + (1 - alpha) * rma[i-1]
    
    return rma[-1]  # Return latest value

@njit(cache=True)
def calculate_ut_bot_signals(closes, highs, lows, atr_period=1, sensitivity=1.0):
    """
    Ultra-fast UT Bot signal calculation
    Returns: (buy_signals, sell_signals) as boolean arrays
    """
    n = len(closes)
    if n < atr_period + 1:
        return np.zeros(n, dtype=np.bool_), np.zeros(n, dtype=np.bool_)
    
    # Calculate HL2
    hl2 = calculate_hl2(highs, lows)
    
    # Calculate True Range
    tr = calculate_true_range(highs, lows, closes)
    
    # Calculate ATR using RMA
    atr = calculate_rma(tr, atr_period)
    
    # Calculate UT Bot levels
    nLoss = sensitivity * atr
    
    # Initialize arrays
    src = hl2
    xATRTrailingStop = np.zeros(n, dtype=np.float64)
    pos = np.zeros(n, dtype=np.int8)
    
    # Calculate trailing stop
    for i in range(1, n):
        if src[i] > xATRTrailingStop[i-1] and src[i-1] > xATRTrailingStop[i-1]:
            xATRTrailingStop[i] = max(xATRTrailingStop[i-1], src[i] - nLoss)
        elif src[i] < xATRTrailingStop[i-1] and src[i-1] < xATRTrailingStop[i-1]:
            xATRTrailingStop[i] = min(xATRTrailingStop[i-1], src[i] + nLoss)
        else:
            if src[i] > xATRTrailingStop[i-1]:
                xATRTrailingStop[i] = src[i] - nLoss
            else:
                xATRTrailingStop[i] = src[i] + nLoss
    
    # Calculate position
    for i in range(1, n):
        if src[i-1] <= xATRTrailingStop[i-1] and src[i] > xATRTrailingStop[i]:
            pos[i] = 1
        elif src[i-1] >= xATRTrailingStop[i-1] and src[i] < xATRTrailingStop[i]:
            pos[i] = -1
        else:
            pos[i] = pos[i-1]
    
    # Generate signals
    buy_signals = np.zeros(n, dtype=np.bool_)
    sell_signals = np.zeros(n, dtype=np.bool_)
    
    for i in range(1, n):
        if pos[i] == 1 and pos[i-1] != 1:
            buy_signals[i] = True
        elif pos[i] == -1 and pos[i-1] != -1:
            sell_signals[i] = True
    
    return buy_signals, sell_signals

class UltraFastUTBot:
    """Ultra-optimized UT Bot for <10ms signal generation"""
    
    def __init__(self, atr_period=1, sensitivity=1.0, min_periods=10):
        self.atr_period = atr_period
        self.sensitivity = sensitivity
        self.min_periods = min_periods
        
        # Signal tracking
        self.last_signal = None
        self.signal_count = 0
        
        # Performance tracking
        self.processing_times = deque(maxlen=100)
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"⚡ Ultra-Fast UT Bot initialized")
        self.logger.info(f"📊 Parameters: ATR={atr_period}, Sensitivity={sensitivity}")
    
    def generate_signal_ultra_fast(self, renko_arrays):
        """
        Ultra-fast signal generation from NumPy arrays
        Args:
            renko_arrays: dict with 'closes', 'highs', 'lows' numpy arrays
        Returns: dict with signal info or None
        """
        start_time = time.perf_counter()
        
        try:
            # Extract arrays
            closes = renko_arrays['closes']
            highs = renko_arrays['highs']
            lows = renko_arrays['lows']
            
            # Check minimum data requirement
            if len(closes) < self.min_periods:
                return None
            
            # Generate signals using JIT-compiled function
            buy_signals, sell_signals = calculate_ut_bot_signals(
                closes, highs, lows, self.atr_period, self.sensitivity
            )
            
            # Get latest signals
            latest_buy = buy_signals[-1]
            latest_sell = sell_signals[-1]
            
            # Create signal object
            signal = {
                'timestamp': time.time(),
                'price': float(closes[-1]),
                'buy_signal': bool(latest_buy),
                'sell_signal': bool(latest_sell),
                'signal_type': 'NONE',
                'brick_count': len(closes)
            }
            
            # Determine signal type
            if latest_buy and not latest_sell:
                signal['signal_type'] = 'BUY'
                self.signal_count += 1
                self.logger.info(f"🟢 ULTRA-FAST BUY #{self.signal_count} at {signal['price']:.5f}")
                
            elif latest_sell and not latest_buy:
                signal['signal_type'] = 'SELL'
                self.signal_count += 1
                self.logger.info(f"🔴 ULTRA-FAST SELL #{self.signal_count} at {signal['price']:.5f}")
            
            self.last_signal = signal
            return signal
            
        except Exception as e:
            self.logger.error(f"Error in ultra-fast signal generation: {e}")
            return None
            
        finally:
            # Track processing time
            processing_time = (time.perf_counter() - start_time) * 1000
            self.processing_times.append(processing_time)
            
            if processing_time > 10.0:  # Warn if >10ms
                self.logger.warning(f"⚠️ Slow UT Bot processing: {processing_time:.2f}ms")
    
    def is_new_signal(self, signal):
        """Check if this is a new actionable signal"""
        if not signal or signal['signal_type'] == 'NONE':
            return False
        
        if not self.last_signal:
            return True
        
        # Check if signal type changed
        if signal['signal_type'] != self.last_signal['signal_type']:
            return True
        
        return False
    
    def get_performance_stats(self):
        """Get processing performance statistics"""
        if not self.processing_times:
            return {}
        
        times = list(self.processing_times)
        return {
            'avg_processing_ms': np.mean(times),
            'max_processing_ms': np.max(times),
            'min_processing_ms': np.min(times),
            'total_signals': self.signal_count,
            'under_10ms_pct': (np.sum(np.array(times) < 10.0) / len(times)) * 100
        }
    
    def reset(self):
        """Reset signal engine"""
        self.last_signal = None
        self.signal_count = 0
        self.processing_times.clear()
        self.logger.info("⚡ Ultra-Fast UT Bot reset")

class UltraFastSignalManager:
    """Manage ultra-fast signal processing"""
    
    def __init__(self, ultra_fast_utbot):
        self.utbot = ultra_fast_utbot
        self.active_signals = deque(maxlen=10)  # Keep last 10 signals
        
        self.logger = logging.getLogger(__name__)
    
    def process_renko_arrays(self, renko_arrays):
        """Process Renko arrays and generate signals"""
        try:
            # Generate signal
            signal = self.utbot.generate_signal_ultra_fast(renko_arrays)
            
            if not signal:
                return None
            
            # Check if it's a new actionable signal
            if signal['signal_type'] in ['BUY', 'SELL']:
                if self.utbot.is_new_signal(signal):
                    self.active_signals.append(signal)
                    
                    self.logger.info(f"🚨 NEW ULTRA-FAST {signal['signal_type']} SIGNAL!")
                    self.logger.info(f"📊 Price: {signal['price']:.5f}")
                    
                    return signal
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error processing ultra-fast signals: {e}")
            return None
    
    def get_latest_signal(self):
        """Get the most recent signal"""
        if self.active_signals:
            return self.active_signals[-1]
        return None
    
    def clear_active_signals(self):
        """Clear processed signals"""
        self.active_signals.clear()

# Performance test
if __name__ == "__main__":
    print("⚡ Testing Ultra-Fast UT Bot Engine...")
    
    # Create test data (1000 Renko bricks)
    n = 1000
    np.random.seed(42)  # Reproducible results
    
    # Generate realistic Renko data
    base_price = 8555.0
    price_changes = np.random.normal(0, 0.1, n)
    closes = base_price + np.cumsum(price_changes)
    
    # Renko bricks have open = previous close
    opens = np.roll(closes, 1)
    opens[0] = base_price
    
    # For Renko: high = max(open, close), low = min(open, close)
    highs = np.maximum(opens, closes)
    lows = np.minimum(opens, closes)
    
    renko_arrays = {
        'opens': opens.astype(np.float32),
        'highs': highs.astype(np.float32),
        'lows': lows.astype(np.float32),
        'closes': closes.astype(np.float32)
    }
    
    # Create ultra-fast UT Bot
    utbot = UltraFastUTBot(atr_period=1, sensitivity=1.0)
    signal_manager = UltraFastSignalManager(utbot)
    
    print(f"🧪 Processing {n} Renko bricks...")
    
    # Performance test
    start_time = time.perf_counter()
    
    signal = signal_manager.process_renko_arrays(renko_arrays)
    
    total_time = (time.perf_counter() - start_time) * 1000
    
    # Get performance stats
    stats = utbot.get_performance_stats()
    
    print(f"\n⚡ ULTRA-FAST UT BOT RESULTS:")
    print(f"   Total time: {total_time:.2f}ms")
    print(f"   Signal generated: {signal['signal_type'] if signal else 'None'}")
    
    if stats:
        print(f"   Avg processing: {stats['avg_processing_ms']:.3f}ms")
        print(f"   Max processing: {stats['max_processing_ms']:.3f}ms")
        print(f"   Under 10ms: {stats['under_10ms_pct']:.1f}%")
    
    print(f"\n✅ Ultra-Fast UT Bot test completed!")
