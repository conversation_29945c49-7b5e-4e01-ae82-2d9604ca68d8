#!/usr/bin/env python3
"""
Live Trading Strategy with Reversal Mechanisms
Implements the EXACT same strategy as ut_bot_backtest.py for live trading

Features:
- TP: 5 bricks (0.5 points)
- SL: 2 bricks (0.2 points) 
- Reversal after SL: 2 brick TP, 5 brick SL
- Dynamic position sizing
- Commission handling
"""

import logging
import time
from datetime import datetime
from enum import Enum

class TradeOutcome(Enum):
    LONG_TP = "LONG_TP"
    LONG_SL = "LONG_SL"
    SHORT_TP = "SHORT_TP"
    SHORT_SL = "SHORT_SL"
    LONG_TP_AFTER_SHORT_SL = "LONG_TP_AFTER_SHORT_SL"
    SHORT_TP_AFTER_LONG_SL = "SHORT_TP_AFTER_LONG_SL"
    LONG_SL_AFTER_SHORT_SL = "LONG_SL_AFTER_SHORT_SL"
    SHORT_SL_AFTER_LONG_SL = "SHORT_SL_AFTER_LONG_SL"
    TIME_EXIT = "TIME_EXIT"

class LiveTrade:
    """Represents an active live trade"""
    
    def __init__(self, trade_id, position_type, entry_price, entry_time, volume, ticket=None):
        self.trade_id = trade_id
        self.position_type = position_type  # "LONG" or "SHORT"
        self.entry_price = entry_price
        self.entry_time = entry_time
        self.volume = volume
        self.ticket = ticket
        
        # TP/SL tracking (in bricks)
        self.tp_bricks = 5  # Take profit at 5 bricks
        self.sl_bricks = 2  # Stop loss at 2 bricks
        
        # Reversal state
        self.in_reversal = False
        self.reversal_tp_bricks = 2
        self.reversal_sl_bricks = 5
        
        # Trade outcome
        self.outcome = None
        self.exit_price = None
        self.exit_time = None
        self.profit = 0.0
        self.commission = 0.0
        
        # Constants from backtest
        self.BRICK_SIZE = 0.1
        self.SPREAD = 0.0
        self.COMMISSION_RATE = 0.15  # 15% commission on profits

class LiveTradingStrategy:
    """Live trading strategy with reversal mechanisms"""
    
    def __init__(self, order_manager):
        self.order_manager = order_manager
        self.active_trades = {}  # trade_id -> LiveTrade
        self.trade_counter = 0
        self.total_profit = 0.0
        self.total_commission = 0.0
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        self.logger.info("Live Trading Strategy initialized with reversal mechanisms")
    
    def calculate_risk_percentage(self, equity):
        """Calculate dynamic risk percentage based on equity"""
        if equity <= 10:
            return 0.06  # 6% for small accounts
        elif equity <= 50:
            return 0.04  # 4% for medium accounts
        else:
            return 0.02  # 2% for larger accounts
    
    def calculate_position_size(self, equity, price_risk):
        """Calculate position size based on risk management"""
        risk_percentage = self.calculate_risk_percentage(equity)
        risk_amount = equity * risk_percentage
        
        # Position size calculation (from backtest)
        lot_size = risk_amount / (price_risk * 10)
        
        # Ensure minimum and maximum limits
        lot_size = max(0.01, min(lot_size, 1.0))
        
        return round(lot_size, 2)
    
    def apply_commission(self, gross_profit):
        """Apply commission to gross profit (15% on profits only)"""
        if gross_profit > 0:
            commission = gross_profit * self.COMMISSION_RATE
            net_profit = gross_profit - commission
            return net_profit, commission
        else:
            return gross_profit, 0.0
    
    def process_signal(self, signal, current_price, current_time, account_info):
        """Process UT Bot signal and execute trade"""
        try:
            if signal['signal_type'] not in ['BUY', 'SELL']:
                return False
            
            # Check if we already have an active trade
            if self.active_trades:
                self.logger.info("Active trade exists - skipping new signal")
                return False
            
            # Calculate position size
            equity = account_info['equity']
            price_risk = 2 * 0.1 + 0.0  # 2 bricks SL + spread
            volume = self.calculate_position_size(equity, price_risk)
            
            # Execute order
            self.trade_counter += 1
            position_type = "LONG" if signal['signal_type'] == 'BUY' else "SHORT"
            
            if signal['signal_type'] == 'BUY':
                result = self.order_manager.execute_buy_order(f"UT Bot LONG #{self.trade_counter}")
            else:
                result = self.order_manager.execute_sell_order(f"UT Bot SHORT #{self.trade_counter}")
            
            if result:
                # Create live trade object
                trade = LiveTrade(
                    trade_id=self.trade_counter,
                    position_type=position_type,
                    entry_price=current_price,
                    entry_time=current_time,
                    volume=volume,
                    ticket=result.order
                )
                
                self.active_trades[self.trade_counter] = trade
                
                self.logger.info(f"Trade #{self.trade_counter} executed - {position_type}")
                self.logger.info(f"Entry price: {current_price:.5f}, Volume: {volume}")
                
                return True
            else:
                self.logger.error("Failed to execute order")
                return False
                
        except Exception as e:
            self.logger.error(f"Error processing signal: {e}")
            return False
    
    def update_trades(self, current_price, current_direction, current_time):
        """Update active trades based on current price movement"""
        try:
            trades_to_remove = []
            
            for trade_id, trade in self.active_trades.items():
                if trade.outcome:  # Trade already closed
                    continue
                
                # Check TP/SL based on brick movement
                if not trade.in_reversal:
                    # Normal TP/SL tracking
                    if trade.position_type == "LONG":
                        if current_direction == 'up':
                            trade.tp_bricks -= 1
                            if trade.tp_bricks == 0:
                                self._close_trade_tp(trade, current_price, current_time)
                                trades_to_remove.append(trade_id)
                        elif current_direction == 'down':
                            trade.sl_bricks -= 1
                            if trade.sl_bricks == 0:
                                self._start_reversal(trade, current_price, current_time, "SHORT")
                    
                    elif trade.position_type == "SHORT":
                        if current_direction == 'down':
                            trade.tp_bricks -= 1
                            if trade.tp_bricks == 0:
                                self._close_trade_tp(trade, current_price, current_time)
                                trades_to_remove.append(trade_id)
                        elif current_direction == 'up':
                            trade.sl_bricks -= 1
                            if trade.sl_bricks == 0:
                                self._start_reversal(trade, current_price, current_time, "LONG")
                
                else:
                    # Reversal TP/SL tracking
                    original_type = trade.position_type
                    reversal_type = "LONG" if original_type == "SHORT" else "SHORT"
                    
                    if reversal_type == "LONG":
                        if current_direction == 'up':
                            trade.reversal_tp_bricks -= 1
                            if trade.reversal_tp_bricks == 0:
                                self._close_reversal_tp(trade, current_price, current_time)
                                trades_to_remove.append(trade_id)
                        elif current_direction == 'down':
                            trade.reversal_sl_bricks -= 1
                            if trade.reversal_sl_bricks == 0:
                                self._close_reversal_sl(trade, current_price, current_time)
                                trades_to_remove.append(trade_id)
                    
                    elif reversal_type == "SHORT":
                        if current_direction == 'down':
                            trade.reversal_tp_bricks -= 1
                            if trade.reversal_tp_bricks == 0:
                                self._close_reversal_tp(trade, current_price, current_time)
                                trades_to_remove.append(trade_id)
                        elif current_direction == 'up':
                            trade.reversal_sl_bricks -= 1
                            if trade.reversal_sl_bricks == 0:
                                self._close_reversal_sl(trade, current_price, current_time)
                                trades_to_remove.append(trade_id)
            
            # Remove completed trades
            for trade_id in trades_to_remove:
                del self.active_trades[trade_id]
                
        except Exception as e:
            self.logger.error(f"Error updating trades: {e}")
    
    def _close_trade_tp(self, trade, exit_price, exit_time):
        """Close trade with take profit"""
        # Close position in MT5
        if trade.ticket:
            self.order_manager.mt5.close_position(trade.ticket)
        
        # Calculate profit
        gross_profit = (5 * trade.BRICK_SIZE - trade.SPREAD) * 10 * trade.volume
        trade.profit, trade.commission = self.apply_commission(gross_profit)
        trade.outcome = TradeOutcome.LONG_TP if trade.position_type == "LONG" else TradeOutcome.SHORT_TP
        trade.exit_price = exit_price
        trade.exit_time = exit_time
        
        self.total_profit += trade.profit
        self.total_commission += trade.commission
        
        self.logger.info(f"{trade.outcome.value} hit. Gross: ${gross_profit:.2f}, Net: ${trade.profit:.2f}")
    
    def _start_reversal(self, trade, current_price, current_time, reversal_direction):
        """Start reversal trade after SL hit"""
        # Close original position
        if trade.ticket:
            self.order_manager.mt5.close_position(trade.ticket)
        
        trade.in_reversal = True
        trade.outcome = TradeOutcome.LONG_SL if trade.position_type == "LONG" else TradeOutcome.SHORT_SL
        
        self.logger.info(f"{trade.outcome.value} hit, starting {reversal_direction} reversal")
        
        # Execute reversal order
        if reversal_direction == "LONG":
            result = self.order_manager.execute_buy_order(f"Reversal LONG after {trade.position_type} SL")
        else:
            result = self.order_manager.execute_sell_order(f"Reversal SHORT after {trade.position_type} SL")
        
        if result:
            trade.ticket = result.order
    
    def _close_reversal_tp(self, trade, exit_price, exit_time):
        """Close reversal trade with take profit"""
        # Close position
        if trade.ticket:
            self.order_manager.mt5.close_position(trade.ticket)
        
        # Calculate profit
        gross_profit = (2 * trade.BRICK_SIZE - trade.SPREAD) * 10 * trade.volume
        trade.profit, trade.commission = self.apply_commission(gross_profit)
        
        if trade.position_type == "LONG":
            trade.outcome = TradeOutcome.SHORT_TP_AFTER_LONG_SL
        else:
            trade.outcome = TradeOutcome.LONG_TP_AFTER_SHORT_SL
        
        trade.exit_price = exit_price
        trade.exit_time = exit_time
        
        self.total_profit += trade.profit
        self.total_commission += trade.commission
        
        self.logger.info(f"{trade.outcome.value} hit. Gross: ${gross_profit:.2f}, Net: ${trade.profit:.2f}")
    
    def _close_reversal_sl(self, trade, exit_price, exit_time):
        """Close reversal trade with stop loss"""
        # Close position
        if trade.ticket:
            self.order_manager.mt5.close_position(trade.ticket)
        
        # Calculate loss
        trade.profit = -(5 * trade.BRICK_SIZE + trade.SPREAD) * 10 * trade.volume
        
        if trade.position_type == "LONG":
            trade.outcome = TradeOutcome.SHORT_SL_AFTER_LONG_SL
        else:
            trade.outcome = TradeOutcome.LONG_SL_AFTER_SHORT_SL
        
        trade.exit_price = exit_price
        trade.exit_time = exit_time
        
        self.total_profit += trade.profit
        
        self.logger.info(f"{trade.outcome.value} hit. Loss: ${trade.profit:.2f}")
    
    def get_strategy_summary(self):
        """Get trading strategy summary"""
        return {
            'total_trades': self.trade_counter,
            'active_trades': len(self.active_trades),
            'total_profit': self.total_profit,
            'total_commission': self.total_commission,
            'net_profit': self.total_profit
        }
