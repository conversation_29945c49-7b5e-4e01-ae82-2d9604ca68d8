#!/usr/bin/env python3
"""
Order Manager for Live Trading
Handle order execution, position management, and risk controls

Ultra-fast execution: <200ms per order
Real money trading - no simulations
"""

import MetaTrader5 as mt5
import time
import logging
from datetime import datetime
from enum import Enum

class OrderType(Enum):
    BUY = mt5.ORDER_TYPE_BUY
    SELL = mt5.ORDER_TYPE_SELL

class PositionType(Enum):
    LONG = mt5.POSITION_TYPE_BUY
    SHORT = mt5.POSITION_TYPE_SELL

class OrderManager:
    """Manage live order execution and positions"""
    
    def __init__(self, mt5_connection, initial_lot_size=0.01, max_lot_size=1.0):
        self.mt5 = mt5_connection
        self.initial_lot_size = initial_lot_size
        self.max_lot_size = max_lot_size
        
        # Trading state
        self.current_position = None
        self.order_count = 0
        self.total_profit = 0.0
        
        # Risk management
        self.max_daily_loss = -100.0  # Max $100 daily loss
        self.daily_profit = 0.0
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("💼 Order Manager initialized")
        self.logger.info(f"📊 Initial lot size: {initial_lot_size}")
        self.logger.info(f"📊 Max lot size: {max_lot_size}")
    
    def calculate_position_size(self, account_balance):
        """Calculate dynamic position size based on account balance"""
        try:
            # Dynamic position sizing: 1% of account balance per $100
            base_lot = max(0.01, account_balance / 10000)  # $10k = 0.01 lot
            
            # Cap at max lot size
            lot_size = min(base_lot, self.max_lot_size)
            
            # Round to 2 decimal places (MT5 standard)
            lot_size = round(lot_size, 2)
            
            self.logger.debug(f"💰 Position size: {lot_size} lots for ${account_balance:.2f} balance")
            return lot_size
            
        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            return self.initial_lot_size
    
    def execute_buy_order(self, comment="UT Bot BUY"):
        """Execute BUY market order"""
        try:
            # Get account info for position sizing
            account_info = self.mt5.get_account_info()
            if not account_info:
                self.logger.error("Cannot get account info for position sizing")
                return None
            
            # Calculate position size
            lot_size = self.calculate_position_size(account_info['balance'])
            
            # Execute order
            start_time = time.time()
            result = self.mt5.send_order(
                action=OrderType.BUY.value,
                volume=lot_size,
                comment=comment
            )
            execution_time = (time.time() - start_time) * 1000  # ms
            
            if result:
                self.order_count += 1
                self.current_position = {
                    'ticket': result.order,
                    'type': 'BUY',
                    'volume': lot_size,
                    'open_price': result.price,
                    'open_time': datetime.now(),
                    'comment': comment
                }
                
                self.logger.info(f"✅ BUY ORDER EXECUTED")
                self.logger.info(f"🎫 Ticket: {result.order}")
                self.logger.info(f"📊 Volume: {lot_size} lots")
                self.logger.info(f"💱 Price: {result.price:.5f}")
                self.logger.info(f"⚡ Execution time: {execution_time:.1f}ms")
                
                return result
            else:
                self.logger.error("❌ BUY order failed")
                return None
                
        except Exception as e:
            self.logger.error(f"Error executing BUY order: {e}")
            return None
    
    def execute_sell_order(self, comment="UT Bot SELL"):
        """Execute SELL market order"""
        try:
            # Get account info for position sizing
            account_info = self.mt5.get_account_info()
            if not account_info:
                self.logger.error("Cannot get account info for position sizing")
                return None
            
            # Calculate position size
            lot_size = self.calculate_position_size(account_info['balance'])
            
            # Execute order
            start_time = time.time()
            result = self.mt5.send_order(
                action=OrderType.SELL.value,
                volume=lot_size,
                comment=comment
            )
            execution_time = (time.time() - start_time) * 1000  # ms
            
            if result:
                self.order_count += 1
                self.current_position = {
                    'ticket': result.order,
                    'type': 'SELL',
                    'volume': lot_size,
                    'open_price': result.price,
                    'open_time': datetime.now(),
                    'comment': comment
                }
                
                self.logger.info(f"✅ SELL ORDER EXECUTED")
                self.logger.info(f"🎫 Ticket: {result.order}")
                self.logger.info(f"📊 Volume: {lot_size} lots")
                self.logger.info(f"💱 Price: {result.price:.5f}")
                self.logger.info(f"⚡ Execution time: {execution_time:.1f}ms")
                
                return result
            else:
                self.logger.error("❌ SELL order failed")
                return None
                
        except Exception as e:
            self.logger.error(f"Error executing SELL order: {e}")
            return None
    
    def close_current_position(self, comment="UT Bot CLOSE"):
        """Close the current open position"""
        try:
            if not self.current_position:
                self.logger.warning("No current position to close")
                return False
            
            # Close position
            start_time = time.time()
            success = self.mt5.close_position(self.current_position['ticket'])
            execution_time = (time.time() - start_time) * 1000  # ms
            
            if success:
                # Calculate profit
                positions = self.mt5.get_positions()
                closed_position = None
                
                # Position might be closed, so get from history if needed
                # For now, just log the close
                self.logger.info(f"✅ POSITION CLOSED")
                self.logger.info(f"🎫 Ticket: {self.current_position['ticket']}")
                self.logger.info(f"⚡ Execution time: {execution_time:.1f}ms")
                
                # Clear current position
                self.current_position = None
                return True
            else:
                self.logger.error("❌ Failed to close position")
                return False
                
        except Exception as e:
            self.logger.error(f"Error closing position: {e}")
            return False
    
    def process_signal(self, signal):
        """Process UT Bot signal and execute appropriate action"""
        try:
            if not signal or signal['signal_type'] == 'NONE':
                return False
            
            signal_type = signal['signal_type']
            current_price = signal['price']
            
            self.logger.info(f"🚨 Processing {signal_type} signal at {current_price:.5f}")
            
            # Check if we have an opposite position to close first
            if self.current_position:
                current_pos_type = self.current_position['type']
                
                if (signal_type == 'BUY' and current_pos_type == 'SELL') or \
                   (signal_type == 'SELL' and current_pos_type == 'BUY'):
                    # Close opposite position first
                    self.logger.info(f"🔄 Closing opposite {current_pos_type} position")
                    self.close_current_position(f"Close {current_pos_type} for {signal_type}")
                    time.sleep(0.1)  # Small delay between close and open
            
            # Execute new order
            if signal_type == 'BUY':
                result = self.execute_buy_order(f"UT Bot BUY #{self.order_count + 1}")
            elif signal_type == 'SELL':
                result = self.execute_sell_order(f"UT Bot SELL #{self.order_count + 1}")
            else:
                return False
            
            return result is not None
            
        except Exception as e:
            self.logger.error(f"Error processing signal: {e}")
            return False
    
    def get_current_positions(self):
        """Get all current open positions"""
        try:
            positions = self.mt5.get_positions()
            return positions
        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return []
    
    def check_risk_limits(self):
        """Check if risk limits are exceeded"""
        try:
            account_info = self.mt5.get_account_info()
            if not account_info:
                return True  # Allow trading if can't check
            
            # Check daily loss limit
            current_profit = account_info['profit']
            
            if current_profit < self.max_daily_loss:
                self.logger.warning(f"⚠️ Daily loss limit reached: ${current_profit:.2f}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking risk limits: {e}")
            return True  # Allow trading if can't check
    
    def get_trading_summary(self):
        """Get trading session summary"""
        try:
            account_info = self.mt5.get_account_info()
            positions = self.get_current_positions()
            
            return {
                'orders_executed': self.order_count,
                'current_positions': len(positions),
                'account_balance': account_info['balance'] if account_info else 0,
                'account_equity': account_info['equity'] if account_info else 0,
                'current_profit': account_info['profit'] if account_info else 0,
                'current_position': self.current_position
            }
            
        except Exception as e:
            self.logger.error(f"Error getting trading summary: {e}")
            return {}

# Test the Order Manager
if __name__ == "__main__":
    print("💼 Testing Order Manager...")
    
    # Note: This test requires actual MT5 connection
    # In real implementation, this would connect to MT5
    
    print("⚠️ Order Manager test requires live MT5 connection")
    print("✅ Order Manager module loaded successfully")
    print("🚀 Ready for live trading integration!")
