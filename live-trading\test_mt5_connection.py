#!/usr/bin/env python3
"""
MT5 Connection Test Script
Test connection to Deriv Demo account and verify trading capabilities

Account: ********
Server: Deriv-Demo
Password: @Ripper25
"""

import MetaTrader5 as mt5
import pandas as pd
import time
from datetime import datetime

def test_mt5_connection():
    """Test MT5 connection and basic functionality"""
    print("🔌 Testing MT5 Connection...")
    print("=" * 50)
    
    # Initialize MT5
    if not mt5.initialize():
        print(f"❌ MT5 initialization failed: {mt5.last_error()}")
        return False
    
    print("✅ MT5 initialized successfully")
    
    # Login to account
    login = ********
    password = "@Ripper25"
    server = "Deriv-Demo"
    
    print(f"🔐 Logging in to account {login} on {server}...")
    
    if not mt5.login(login, password, server):
        print(f"❌ Login failed: {mt5.last_error()}")
        mt5.shutdown()
        return False
    
    print("✅ Login successful")
    
    # Get account info
    account_info = mt5.account_info()
    if account_info is None:
        print("❌ Failed to get account info")
        mt5.shutdown()
        return False
    
    print("\n📊 Account Information:")
    print(f"  Login: {account_info.login}")
    print(f"  Server: {account_info.server}")
    print(f"  Balance: ${account_info.balance:.2f}")
    print(f"  Equity: ${account_info.equity:.2f}")
    print(f"  Margin: ${account_info.margin:.2f}")
    print(f"  Free Margin: ${account_info.margin_free:.2f}")
    print(f"  Leverage: 1:{account_info.leverage}")
    print(f"  Currency: {account_info.currency}")
    
    # Test Step Index symbol specifically
    print(f"\n📈 Searching for Step Index symbol...")

    # List of possible Step Index symbol names
    step_index_symbols = [
        "Step Index",
        "STEP Index",
        "Step_Index",
        "STEP_Index",
        "stpRNG",
        "Volatility 100 Index",
        "R_100"
    ]

    symbol = None
    symbol_info = None

    for test_symbol in step_index_symbols:
        print(f"🔍 Trying symbol: {test_symbol}")
        test_info = mt5.symbol_info(test_symbol)
        if test_info is not None:
            symbol = test_symbol
            symbol_info = test_info
            print(f"✅ Found Step Index as: {symbol}")
            break

    if symbol_info is None:
        print(f"❌ Step Index symbol not found with any name")
    
    if symbol_info is not None:
        print(f"✅ Symbol {symbol} found:")
        print(f"  Bid: {symbol_info.bid}")
        print(f"  Ask: {symbol_info.ask}")
        print(f"  Spread: {symbol_info.spread}")
        print(f"  Min Volume: {symbol_info.volume_min}")
        print(f"  Max Volume: {symbol_info.volume_max}")
        print(f"  Volume Step: {symbol_info.volume_step}")
        print(f"  Digits: {symbol_info.digits}")
        print(f"  Point: {symbol_info.point}")
        print(f"  Trade Mode: {symbol_info.trade_mode}")
    else:
        print(f"❌ No suitable symbol found for trading")
        
    # Get available symbols
    print(f"\n📋 Available symbols (first 20):")
    symbols = mt5.symbols_get()
    if symbols is not None:
        for i, sym in enumerate(symbols[:20]):
            print(f"  {i+1}. {sym.name} - {sym.description}")
    
    # Test tick data
    if symbol_info is not None:
        print(f"\n📊 Testing tick data for {symbol}...")
        tick = mt5.symbol_info_tick(symbol)
        if tick is not None:
            print(f"  Time: {datetime.fromtimestamp(tick.time)}")
            print(f"  Bid: {tick.bid}")
            print(f"  Ask: {tick.ask}")
            print(f"  Volume: {tick.volume}")
        else:
            print(f"❌ Failed to get tick data for {symbol}")
    
    # Test positions
    print(f"\n💼 Current positions:")
    positions = mt5.positions_get()
    if positions is not None and len(positions) > 0:
        for pos in positions:
            print(f"  {pos.symbol}: {pos.type_str} {pos.volume} @ {pos.price_open}")
    else:
        print("  No open positions")
    
    # Test orders
    print(f"\n📋 Pending orders:")
    orders = mt5.orders_get()
    if orders is not None and len(orders) > 0:
        for order in orders:
            print(f"  {order.symbol}: {order.type_str} {order.volume} @ {order.price_open}")
    else:
        print("  No pending orders")
    
    # Test a small order (if symbol is available)
    if symbol_info is not None and symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_FULL:
        print(f"\n⚡ Testing order execution for {symbol}...")
        
        # Prepare order request
        request = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": symbol_info.volume_min,
            "type": mt5.ORDER_TYPE_BUY,
            "price": symbol_info.ask,
            "deviation": 20,
            "magic": 234000,
            "comment": "MT5_Connection_Test",
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
        }
        
        print("🚀 Sending test order...")
        result = mt5.order_send(request)
        
        if result is not None:
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                print(f"✅ Test order successful!")
                print(f"  Order: {result.order}")
                print(f"  Deal: {result.deal}")
                print(f"  Volume: {result.volume}")
                print(f"  Price: {result.price}")
                
                # Close the test position immediately
                print("🔄 Closing test position...")
                close_request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": symbol,
                    "volume": result.volume,
                    "type": mt5.ORDER_TYPE_SELL,
                    "position": result.order,
                    "price": symbol_info.bid,
                    "deviation": 20,
                    "magic": 234000,
                    "comment": "Close_Test_Position",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_IOC,
                }
                
                close_result = mt5.order_send(close_request)
                if close_result is not None and close_result.retcode == mt5.TRADE_RETCODE_DONE:
                    print("✅ Test position closed successfully")
                else:
                    print(f"⚠️ Failed to close test position: {close_result.comment if close_result else 'Unknown error'}")
                    
            else:
                print(f"❌ Test order failed: {result.comment}")
        else:
            print(f"❌ Order send failed: {mt5.last_error()}")
    else:
        print(f"⚠️ Trading not available for {symbol}")
    
    # Cleanup
    mt5.shutdown()
    print("\n✅ MT5 connection test completed")
    return True

def main():
    """Main test function"""
    print("🧪 MT5 Connection Test for Ultra-Fast Trading System")
    print("Account: ******** (Deriv-Demo)")
    print("Target Symbol: stpRNG")
    print("=" * 60)
    
    try:
        success = test_mt5_connection()
        
        if success:
            print("\n🎉 MT5 connection test PASSED!")
            print("✅ Ready for ultra-fast live trading")
        else:
            print("\n❌ MT5 connection test FAILED!")
            print("Please check your MT5 installation and credentials")
            
    except Exception as e:
        print(f"\n💥 Test error: {e}")
        print("Please ensure MT5 is installed and running")

if __name__ == "__main__":
    main()
