#!/usr/bin/env python3
"""
Live Trading Configuration
Centralized configuration management for the live trading system

Features:
- Environment-specific settings
- Trading parameters
- Risk management settings
- API credentials
- Performance tuning
"""

import os
from typing import Dict, Any
from dataclasses import dataclass

@dataclass
class DerivConfig:
    """Deriv.com API configuration"""
    app_id: str = "71058"
    token: str = "wZ9epOSeoux3O8g"  # From memory
    websocket_url: str = "wss://ws.derivws.com/websockets/v3"
    reconnect_attempts: int = 10
    ping_interval: int = 30
    buffer_size: int = 10000

@dataclass
class MT5Config:
    """MetaTrader 5 configuration"""
    login: int = 40496559
    password: str = "@Ripper25"
    server: str = "Deriv-Demo"
    timeout: int = 60000
    max_retries: int = 5
    order_deviation: int = 20
    magic_number: int = 234000

@dataclass
class TradingConfig:
    """Core trading strategy configuration"""
    symbol: str = "stpRNG"
    brick_size: float = 0.1
    atr_period: int = 1
    sensitivity: float = 1.0
    use_heikin_ashi: bool = False
    signal_cooldown: int = 5  # seconds
    max_positions: int = 1
    
@dataclass
class RiskConfig:
    """Risk management configuration"""
    initial_balance: float = 10.0
    base_risk_percent: float = 4.0  # 4%
    max_risk_percent: float = 6.0   # 6%
    min_risk_percent: float = 1.0   # 1%
    max_drawdown_percent: float = 25.0  # 25%
    stop_loss_bricks: int = 2
    take_profit_bricks: int = 5
    max_consecutive_losses: int = 10

@dataclass
class PerformanceConfig:
    """Performance and optimization settings"""
    tick_buffer_size: int = 5000
    renko_buffer_size: int = 1000
    signal_buffer_size: int = 100
    log_level: str = "INFO"
    enable_performance_monitoring: bool = True
    save_tick_data: bool = False
    save_trade_history: bool = True

class LiveTradingConfig:
    """Main configuration class for live trading system"""
    
    def __init__(self, environment: str = "development"):
        self.environment = environment
        
        # Load configurations
        self.deriv = DerivConfig()
        self.mt5 = MT5Config()
        self.trading = TradingConfig()
        self.risk = RiskConfig()
        self.performance = PerformanceConfig()
        
        # Load environment-specific overrides
        self._load_environment_config()
        
        # Load from environment variables if available
        self._load_from_env()
        
    def _load_environment_config(self):
        """Load environment-specific configuration overrides"""
        if self.environment == "production":
            # Production settings
            self.performance.log_level = "WARNING"
            self.performance.save_tick_data = False
            self.deriv.buffer_size = 20000
            self.risk.max_risk_percent = 4.0  # More conservative in production
            
        elif self.environment == "testing":
            # Testing settings
            self.performance.log_level = "DEBUG"
            self.performance.save_tick_data = True
            self.risk.initial_balance = 100.0  # Larger test balance
            self.trading.signal_cooldown = 1  # Faster signals for testing
            
        elif self.environment == "development":
            # Development settings
            self.performance.log_level = "DEBUG"
            self.performance.save_tick_data = True
            self.performance.enable_performance_monitoring = True
            
    def _load_from_env(self):
        """Load configuration from environment variables"""
        # Deriv configuration
        if os.getenv("DERIV_APP_ID"):
            self.deriv.app_id = os.getenv("DERIV_APP_ID")
        if os.getenv("DERIV_TOKEN"):
            self.deriv.token = os.getenv("DERIV_TOKEN")
            
        # MT5 configuration
        if os.getenv("MT5_LOGIN"):
            self.mt5.login = int(os.getenv("MT5_LOGIN"))
        if os.getenv("MT5_PASSWORD"):
            self.mt5.password = os.getenv("MT5_PASSWORD")
        if os.getenv("MT5_SERVER"):
            self.mt5.server = os.getenv("MT5_SERVER")
            
        # Trading configuration
        if os.getenv("TRADING_SYMBOL"):
            self.trading.symbol = os.getenv("TRADING_SYMBOL")
        if os.getenv("BRICK_SIZE"):
            self.trading.brick_size = float(os.getenv("BRICK_SIZE"))
            
        # Risk configuration
        if os.getenv("INITIAL_BALANCE"):
            self.risk.initial_balance = float(os.getenv("INITIAL_BALANCE"))
        if os.getenv("MAX_RISK_PERCENT"):
            self.risk.max_risk_percent = float(os.getenv("MAX_RISK_PERCENT"))
            
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'environment': self.environment,
            'deriv': {
                'app_id': self.deriv.app_id,
                'websocket_url': self.deriv.websocket_url,
                'reconnect_attempts': self.deriv.reconnect_attempts,
                'ping_interval': self.deriv.ping_interval,
                'buffer_size': self.deriv.buffer_size
            },
            'mt5': {
                'login': self.mt5.login,
                'server': self.mt5.server,
                'timeout': self.mt5.timeout,
                'max_retries': self.mt5.max_retries,
                'order_deviation': self.mt5.order_deviation,
                'magic_number': self.mt5.magic_number
            },
            'trading': {
                'symbol': self.trading.symbol,
                'brick_size': self.trading.brick_size,
                'atr_period': self.trading.atr_period,
                'sensitivity': self.trading.sensitivity,
                'use_heikin_ashi': self.trading.use_heikin_ashi,
                'signal_cooldown': self.trading.signal_cooldown,
                'max_positions': self.trading.max_positions
            },
            'risk': {
                'initial_balance': self.risk.initial_balance,
                'base_risk_percent': self.risk.base_risk_percent,
                'max_risk_percent': self.risk.max_risk_percent,
                'min_risk_percent': self.risk.min_risk_percent,
                'max_drawdown_percent': self.risk.max_drawdown_percent,
                'stop_loss_bricks': self.risk.stop_loss_bricks,
                'take_profit_bricks': self.risk.take_profit_bricks,
                'max_consecutive_losses': self.risk.max_consecutive_losses
            },
            'performance': {
                'tick_buffer_size': self.performance.tick_buffer_size,
                'renko_buffer_size': self.performance.renko_buffer_size,
                'signal_buffer_size': self.performance.signal_buffer_size,
                'log_level': self.performance.log_level,
                'enable_performance_monitoring': self.performance.enable_performance_monitoring,
                'save_tick_data': self.performance.save_tick_data,
                'save_trade_history': self.performance.save_trade_history
            }
        }
        
    def validate(self) -> bool:
        """Validate configuration settings"""
        errors = []
        
        # Validate Deriv config
        if not self.deriv.app_id:
            errors.append("Deriv app_id is required")
            
        # Validate trading config
        if self.trading.brick_size <= 0:
            errors.append("Brick size must be positive")
        if self.trading.atr_period < 1:
            errors.append("ATR period must be at least 1")
        if self.trading.sensitivity <= 0:
            errors.append("Sensitivity must be positive")
            
        # Validate risk config
        if self.risk.initial_balance <= 0:
            errors.append("Initial balance must be positive")
        if not (0 < self.risk.min_risk_percent <= self.risk.max_risk_percent <= 100):
            errors.append("Risk percentages must be valid (0 < min <= max <= 100)")
        if self.risk.max_drawdown_percent <= 0 or self.risk.max_drawdown_percent > 100:
            errors.append("Max drawdown must be between 0 and 100 percent")
            
        if errors:
            print("❌ Configuration validation errors:")
            for error in errors:
                print(f"  - {error}")
            return False
            
        return True
        
    def get_mt5_credentials(self) -> Dict[str, Any]:
        """Get MT5 credentials (excluding password from logs)"""
        return {
            'login': self.mt5.login,
            'server': self.mt5.server,
            'has_password': bool(self.mt5.password)
        }
        
    def update_from_dict(self, config_dict: Dict[str, Any]):
        """Update configuration from dictionary"""
        if 'trading' in config_dict:
            trading_config = config_dict['trading']
            if 'symbol' in trading_config:
                self.trading.symbol = trading_config['symbol']
            if 'brick_size' in trading_config:
                self.trading.brick_size = float(trading_config['brick_size'])
            if 'atr_period' in trading_config:
                self.trading.atr_period = int(trading_config['atr_period'])
            if 'sensitivity' in trading_config:
                self.trading.sensitivity = float(trading_config['sensitivity'])
                
        if 'risk' in config_dict:
            risk_config = config_dict['risk']
            if 'initial_balance' in risk_config:
                self.risk.initial_balance = float(risk_config['initial_balance'])
            if 'max_risk_percent' in risk_config:
                self.risk.max_risk_percent = float(risk_config['max_risk_percent'])

# Predefined configurations for different scenarios
CONFIGS = {
    'conservative': {
        'risk': {
            'base_risk_percent': 2.0,
            'max_risk_percent': 3.0,
            'max_drawdown_percent': 15.0
        },
        'trading': {
            'signal_cooldown': 10
        }
    },
    'aggressive': {
        'risk': {
            'base_risk_percent': 6.0,
            'max_risk_percent': 8.0,
            'max_drawdown_percent': 35.0
        },
        'trading': {
            'signal_cooldown': 2
        }
    },
    'scalping': {
        'trading': {
            'brick_size': 0.05,
            'signal_cooldown': 1,
            'take_profit_bricks': 3,
            'stop_loss_bricks': 1
        },
        'performance': {
            'tick_buffer_size': 20000
        }
    }
}

def create_config(environment: str = "development", preset: str = None) -> LiveTradingConfig:
    """Create configuration with optional preset"""
    config = LiveTradingConfig(environment)
    
    if preset and preset in CONFIGS:
        config.update_from_dict(CONFIGS[preset])
        
    return config

# Example usage
if __name__ == "__main__":
    # Create default config
    config = create_config("development")
    
    print("🔧 Live Trading Configuration")
    print(f"Environment: {config.environment}")
    print(f"Symbol: {config.trading.symbol}")
    print(f"Brick Size: {config.trading.brick_size}")
    print(f"Initial Balance: ${config.risk.initial_balance}")
    print(f"Max Risk: {config.risk.max_risk_percent}%")
    
    # Validate configuration
    if config.validate():
        print("✅ Configuration is valid")
    else:
        print("❌ Configuration validation failed")
        
    # Test preset configurations
    print("\n📋 Available Presets:")
    for preset_name in CONFIGS.keys():
        preset_config = create_config("development", preset_name)
        print(f"  {preset_name}: Risk {preset_config.risk.base_risk_percent}%, Cooldown {preset_config.trading.signal_cooldown}s")
