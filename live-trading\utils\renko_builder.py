#!/usr/bin/env python3
"""
High-Performance Real-Time Renko Chart Builder
Optimized for lightning-fast brick generation from live tick data

Features:
- Real-time brick formation
- Memory-efficient circular buffer
- Thread-safe operations
- Multiple timeframe support
- Direction tracking
"""

import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Deque
from collections import deque
import logging

class RenkoBrick:
    def __init__(self, open_price: float, close_price: float, direction: str, 
                 start_time: datetime, end_time: datetime = None):
        self.open = open_price
        self.close = close_price
        self.high = max(open_price, close_price)
        self.low = min(open_price, close_price)
        self.direction = direction  # 'up' or 'down'
        self.start_time = start_time
        self.end_time = end_time or start_time
        self.tick_count = 0
        
    def to_dict(self) -> Dict:
        return {
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'direction': self.direction,
            'datetime': self.end_time,
            'start_time': self.start_time,
            'tick_count': self.tick_count
        }

class RenkoBuilder:
    def __init__(self, brick_size: float, buffer_size: int = 1000):
        self.brick_size = brick_size
        self.buffer_size = buffer_size
        
        # Data structures
        self.bricks: Deque[RenkoBrick] = deque(maxlen=buffer_size)
        self.current_brick: Optional[RenkoBrick] = None
        self.last_price = None
        self.last_tick_time = None
        
        # Performance tracking
        self.total_ticks_processed = 0
        self.total_bricks_formed = 0
        self.start_time = time.time()
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Logging
        self.logger = logging.getLogger(__name__)
        
    def add_tick(self, price: float, timestamp: datetime = None) -> Optional[Dict]:
        """
        Add a new tick and return new brick if formed
        Returns None if no new brick is formed
        """
        if timestamp is None:
            timestamp = datetime.now()
            
        with self.lock:
            self.total_ticks_processed += 1
            self.last_tick_time = timestamp
            
            # Initialize first brick if needed
            if self.current_brick is None:
                self.current_brick = RenkoBrick(
                    open_price=price,
                    close_price=price,
                    direction='up',  # Initial direction
                    start_time=timestamp
                )
                self.last_price = price
                return None
                
            # Update current brick tick count
            self.current_brick.tick_count += 1
            self.current_brick.end_time = timestamp
            
            # Check if new brick should be formed
            new_brick = self._check_brick_formation(price, timestamp)
            
            self.last_price = price
            return new_brick.to_dict() if new_brick else None
            
    def _check_brick_formation(self, price: float, timestamp: datetime) -> Optional[RenkoBrick]:
        """Check if a new brick should be formed based on price movement"""
        if not self.current_brick:
            return None
            
        price_diff = price - self.current_brick.close
        
        # Check for upward brick formation
        if price_diff >= self.brick_size:
            num_bricks = int(price_diff / self.brick_size)
            return self._form_bricks(num_bricks, 'up', timestamp)
            
        # Check for downward brick formation
        elif price_diff <= -self.brick_size:
            num_bricks = int(abs(price_diff) / self.brick_size)
            return self._form_bricks(num_bricks, 'down', timestamp)
            
        return None
        
    def _form_bricks(self, num_bricks: int, direction: str, timestamp: datetime) -> Optional[RenkoBrick]:
        """Form one or more bricks in the specified direction"""
        if not self.current_brick or num_bricks <= 0:
            return None
            
        last_formed_brick = None
        
        for i in range(num_bricks):
            # Calculate brick prices
            if direction == 'up':
                new_open = self.current_brick.close
                new_close = new_open + self.brick_size
            else:  # direction == 'down'
                new_open = self.current_brick.close
                new_close = new_open - self.brick_size
                
            # Create new brick
            new_brick = RenkoBrick(
                open_price=new_open,
                close_price=new_close,
                direction=direction,
                start_time=self.current_brick.end_time,
                end_time=timestamp
            )
            
            # Add completed current brick to buffer
            self.bricks.append(self.current_brick)
            self.total_bricks_formed += 1
            
            # Set new brick as current
            self.current_brick = new_brick
            last_formed_brick = new_brick
            
            self.logger.debug(f"Formed {direction} brick: {new_open} -> {new_close}")
            
        return last_formed_brick
        
    def get_recent_bricks(self, count: int = None) -> List[Dict]:
        """Get recent bricks as dictionaries"""
        with self.lock:
            if count is None:
                return [brick.to_dict() for brick in self.bricks]
            else:
                recent_bricks = list(self.bricks)[-count:] if len(self.bricks) >= count else list(self.bricks)
                return [brick.to_dict() for brick in recent_bricks]
                
    def get_current_brick_info(self) -> Optional[Dict]:
        """Get information about the current (incomplete) brick"""
        with self.lock:
            if self.current_brick:
                brick_dict = self.current_brick.to_dict()
                brick_dict['is_current'] = True
                brick_dict['completion_percentage'] = self._calculate_completion_percentage()
                return brick_dict
            return None
            
    def _calculate_completion_percentage(self) -> float:
        """Calculate how close the current brick is to completion"""
        if not self.current_brick or not self.last_price:
            return 0.0
            
        price_diff = abs(self.last_price - self.current_brick.close)
        return min(price_diff / self.brick_size * 100, 100.0)
        
    def get_statistics(self) -> Dict:
        """Get performance and usage statistics"""
        uptime = time.time() - self.start_time
        
        with self.lock:
            tick_rate = self.total_ticks_processed / uptime if uptime > 0 else 0
            brick_rate = self.total_bricks_formed / uptime if uptime > 0 else 0
            
            return {
                'uptime_seconds': uptime,
                'total_ticks_processed': self.total_ticks_processed,
                'total_bricks_formed': self.total_bricks_formed,
                'current_buffer_size': len(self.bricks),
                'max_buffer_size': self.buffer_size,
                'brick_size': self.brick_size,
                'tick_rate_per_second': tick_rate,
                'brick_formation_rate_per_hour': brick_rate * 3600,
                'last_tick_time': self.last_tick_time,
                'current_brick_completion': self._calculate_completion_percentage()
            }
            
    def reset(self):
        """Reset the builder to initial state"""
        with self.lock:
            self.bricks.clear()
            self.current_brick = None
            self.last_price = None
            self.last_tick_time = None
            self.total_ticks_processed = 0
            self.total_bricks_formed = 0
            self.start_time = time.time()
            
        self.logger.info("Renko builder reset")
        
    def set_brick_size(self, new_brick_size: float):
        """Change brick size (will reset the builder)"""
        with self.lock:
            self.brick_size = new_brick_size
            self.reset()
            
        self.logger.info(f"Brick size changed to {new_brick_size}")
        
    def export_to_dataframe(self):
        """Export bricks to pandas DataFrame"""
        try:
            import pandas as pd
            
            with self.lock:
                brick_data = [brick.to_dict() for brick in self.bricks]
                
            if brick_data:
                df = pd.DataFrame(brick_data)
                df['datetime'] = pd.to_datetime(df['datetime'])
                return df
            else:
                return pd.DataFrame()
                
        except ImportError:
            self.logger.error("pandas not available for DataFrame export")
            return None
            
    def get_price_levels(self) -> Dict:
        """Get key price levels from recent bricks"""
        with self.lock:
            if len(self.bricks) == 0:
                return {}
                
            recent_bricks = list(self.bricks)[-20:]  # Last 20 bricks
            
            highs = [brick.high for brick in recent_bricks]
            lows = [brick.low for brick in recent_bricks]
            
            return {
                'highest_high': max(highs),
                'lowest_low': min(lows),
                'recent_high': highs[-1] if highs else None,
                'recent_low': lows[-1] if lows else None,
                'support_levels': self._find_support_levels(recent_bricks),
                'resistance_levels': self._find_resistance_levels(recent_bricks)
            }
            
    def _find_support_levels(self, bricks: List[RenkoBrick]) -> List[float]:
        """Find potential support levels from brick lows"""
        if len(bricks) < 3:
            return []
            
        lows = [brick.low for brick in bricks]
        support_levels = []
        
        # Simple support detection: find lows that are touched multiple times
        for i, low in enumerate(lows):
            touch_count = sum(1 for other_low in lows if abs(other_low - low) < self.brick_size * 0.5)
            if touch_count >= 2:  # At least 2 touches
                support_levels.append(low)
                
        # Remove duplicates and sort
        support_levels = sorted(list(set(support_levels)))
        return support_levels[-3:]  # Return top 3 support levels
        
    def _find_resistance_levels(self, bricks: List[RenkoBrick]) -> List[float]:
        """Find potential resistance levels from brick highs"""
        if len(bricks) < 3:
            return []
            
        highs = [brick.high for brick in bricks]
        resistance_levels = []
        
        # Simple resistance detection: find highs that are touched multiple times
        for i, high in enumerate(highs):
            touch_count = sum(1 for other_high in highs if abs(other_high - high) < self.brick_size * 0.5)
            if touch_count >= 2:  # At least 2 touches
                resistance_levels.append(high)
                
        # Remove duplicates and sort
        resistance_levels = sorted(list(set(resistance_levels)), reverse=True)
        return resistance_levels[:3]  # Return top 3 resistance levels

# Example usage and testing
if __name__ == "__main__":
    import random
    
    # Create Renko builder
    builder = RenkoBuilder(brick_size=0.1, buffer_size=100)
    
    # Simulate tick data
    base_price = 100.0
    current_price = base_price
    
    print("🧱 Testing Renko Builder...")
    
    for i in range(1000):
        # Simulate price movement
        price_change = random.uniform(-0.05, 0.05)
        current_price += price_change
        
        # Add tick
        new_brick = builder.add_tick(current_price)
        
        if new_brick:
            print(f"📊 New brick formed: {new_brick['direction']} {new_brick['open']:.2f} -> {new_brick['close']:.2f}")
            
    # Get statistics
    stats = builder.get_statistics()
    print(f"\n📈 Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
        
    # Get recent bricks
    recent = builder.get_recent_bricks(5)
    print(f"\n🔍 Last 5 bricks:")
    for brick in recent:
        print(f"  {brick['direction']}: {brick['open']:.2f} -> {brick['close']:.2f}")
        
    print("✅ Renko builder test completed")
