#!/usr/bin/env python3
"""
Step Index Trading Test Script
Test UT Bot signals and Step Index order execution

This script:
1. Connects to Deriv WebSocket for stpRNG tick data
2. Generates UT Bot signals
3. Places BUY/SELL orders on Step Index in MT5
4. Monitors performance and execution
"""

import asyncio
import uvloop
import sys
import os
import time
import logging
from datetime import datetime

# Set ultra-fast event loop
asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.ultra_fast_websocket import UltraFastTickCollector, TickData
from strategies.ultra_fast_ut_bot import UltraFastUTBot
from mt5.mt5_manager import MT5Manager, TradeRequest
from config.trading_config import create_config

class StepIndexTradingTest:
    """Test Step Index trading with UT Bot signals"""
    
    def __init__(self):
        self.config = create_config("development")
        
        # Components
        self.tick_collector = None
        self.ut_bot = None
        self.mt5_manager = None
        
        # Test state
        self.running = False
        self.signals_received = 0
        self.orders_placed = 0
        self.successful_orders = 0
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
    async def initialize(self) -> bool:
        """Initialize all components"""
        try:
            self.logger.info("🧪 Initializing Step Index Trading Test...")
            
            # Initialize MT5
            self.logger.info("🔌 Connecting to MT5...")
            self.mt5_manager = MT5Manager(
                login=self.config.mt5.login,
                password=self.config.mt5.password,
                server=self.config.mt5.server
            )
            
            if not self.mt5_manager.initialize():
                raise Exception("Failed to connect to MT5")
                
            # Test Step Index symbol
            step_symbol = self.mt5_manager.find_step_index_symbol()
            if not step_symbol:
                raise Exception("Step Index symbol not found in MT5")
                
            self.logger.info(f"✅ Found Step Index symbol: {step_symbol}")
            
            # Get account info
            account_info = self.mt5_manager.update_account_info()
            self.logger.info(f"✅ MT5 Connected - Balance: ${account_info.get('balance', 0):.2f}")
            
            # Initialize UT Bot
            self.logger.info("🧠 Initializing UT Bot...")
            self.ut_bot = UltraFastUTBot(
                atr_period=1,
                sensitivity=1.0,
                buffer_size=100
            )
            
            # Initialize Tick Collector
            self.logger.info("📡 Connecting to Deriv WebSocket...")
            self.tick_collector = UltraFastTickCollector(
                app_id=self.config.deriv.app_id,
                symbols=[self.config.trading.deriv_symbol],
                buffer_size=1000
            )
            
            # Add tick callback
            self.tick_collector.add_tick_callback(self.on_tick_received)
            
            self.logger.info("✅ All components initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Initialization failed: {e}")
            return False
            
    async def on_tick_received(self, tick: TickData):
        """Process tick and generate signals"""
        try:
            if not self.running:
                return
                
            # Process through UT Bot
            signal = self.ut_bot.add_tick(tick.price)
            
            if signal:
                self.signals_received += 1
                self.logger.info(f"📊 UT Bot Signal #{self.signals_received}: {signal['type']} @ {signal['price']:.5f}")
                
                # Execute trade
                await self.execute_test_trade(signal)
                
        except Exception as e:
            self.logger.error(f"❌ Tick processing error: {e}")
            
    async def execute_test_trade(self, signal):
        """Execute a test trade on Step Index"""
        try:
            self.orders_placed += 1
            
            # Create trade request
            trade_request = TradeRequest(
                symbol="Step Index",  # Will be resolved to actual symbol
                action=signal['type'],
                volume=0.01,  # Minimum volume for testing
                comment=f"Test_UT_{signal['type']}_{self.orders_placed}"
            )
            
            self.logger.info(f"⚡ Placing {signal['type']} order #{self.orders_placed}...")
            
            # Execute order
            result = self.mt5_manager.send_order(trade_request)
            
            if result.success:
                self.successful_orders += 1
                self.logger.info(f"✅ Order #{self.orders_placed} executed successfully!")
                self.logger.info(f"   Order ID: {result.order_id}")
                self.logger.info(f"   Price: {result.price}")
                self.logger.info(f"   Execution Time: {result.execution_time_ms:.1f}ms")
                
                # Close the position after 30 seconds for testing
                asyncio.create_task(self.close_test_position_later(result.order_id, 30))
                
            else:
                self.logger.error(f"❌ Order #{self.orders_placed} failed: {result.error_description}")
                
        except Exception as e:
            self.logger.error(f"❌ Trade execution error: {e}")
            
    async def close_test_position_later(self, order_id, delay_seconds):
        """Close test position after delay"""
        try:
            await asyncio.sleep(delay_seconds)
            
            self.logger.info(f"🔄 Closing test position {order_id} after {delay_seconds}s...")
            
            # Get current positions
            positions = self.mt5_manager.get_positions("Step Index")
            
            for position in positions:
                if position.get('identifier') == order_id:
                    close_result = self.mt5_manager.close_position("Step Index", order_id)
                    if close_result.success:
                        self.logger.info(f"✅ Test position {order_id} closed successfully")
                    else:
                        self.logger.error(f"❌ Failed to close test position {order_id}")
                    break
                    
        except Exception as e:
            self.logger.error(f"❌ Position close error: {e}")
            
    async def run_test(self, duration_minutes=5):
        """Run the trading test for specified duration"""
        if not await self.initialize():
            return False
            
        self.running = True
        start_time = time.time()
        
        self.logger.info(f"🎯 Starting Step Index trading test for {duration_minutes} minutes...")
        self.logger.info("📊 Monitoring stpRNG ticks -> UT Bot signals -> Step Index orders")
        
        try:
            # Start tick collection with timeout
            await asyncio.wait_for(
                self.tick_collector.start(), 
                timeout=duration_minutes * 60
            )
            
        except asyncio.TimeoutError:
            self.logger.info(f"⏰ Test completed after {duration_minutes} minutes")
        except KeyboardInterrupt:
            self.logger.info("🛑 Test interrupted by user")
        except Exception as e:
            self.logger.error(f"❌ Test error: {e}")
        finally:
            await self.stop_test()
            
        # Generate test report
        await self.generate_test_report(time.time() - start_time)
        return True
        
    async def stop_test(self):
        """Stop the test"""
        self.running = False
        
        if self.tick_collector:
            await self.tick_collector.stop()
            
        if self.mt5_manager:
            self.mt5_manager.shutdown()
            
    async def generate_test_report(self, duration):
        """Generate test report"""
        self.logger.info("=" * 60)
        self.logger.info("📊 STEP INDEX TRADING TEST REPORT")
        self.logger.info("=" * 60)
        self.logger.info(f"Test Duration: {duration/60:.1f} minutes")
        self.logger.info(f"Signals Received: {self.signals_received}")
        self.logger.info(f"Orders Placed: {self.orders_placed}")
        self.logger.info(f"Successful Orders: {self.successful_orders}")
        
        if self.orders_placed > 0:
            success_rate = (self.successful_orders / self.orders_placed) * 100
            self.logger.info(f"Success Rate: {success_rate:.1f}%")
            
        if self.tick_collector:
            tick_stats = self.tick_collector.get_performance_stats()
            self.logger.info(f"Total Ticks: {tick_stats['total_ticks']}")
            self.logger.info(f"Tick Rate: {tick_stats['tick_rate']:.1f}/sec")
            
        if self.ut_bot:
            bot_stats = self.ut_bot.get_performance_stats()
            self.logger.info(f"Avg Signal Calc: {bot_stats['recent_average_ms']:.2f}ms")
            
        if self.mt5_manager:
            mt5_stats = self.mt5_manager.get_performance_stats()
            self.logger.info(f"Avg Execution: {mt5_stats['average_execution_time_ms']:.1f}ms")
            
        self.logger.info("=" * 60)
        
        # Performance assessment
        if self.successful_orders > 0:
            self.logger.info("🎉 TEST PASSED - Step Index trading is working!")
        else:
            self.logger.warning("⚠️ TEST INCOMPLETE - No successful orders placed")

async def main():
    """Main test function"""
    print("🧪 Step Index Trading Test")
    print("=" * 50)
    print("This test will:")
    print("1. Connect to Deriv WebSocket (stpRNG)")
    print("2. Generate UT Bot signals")
    print("3. Place orders on Step Index in MT5")
    print("4. Monitor performance")
    print("=" * 50)
    
    test = StepIndexTradingTest()
    
    try:
        # Run test for 5 minutes
        await test.run_test(duration_minutes=5)
        
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted")
        await test.stop_test()
    except Exception as e:
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
