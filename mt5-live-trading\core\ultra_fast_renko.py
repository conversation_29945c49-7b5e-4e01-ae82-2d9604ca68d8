#!/usr/bin/env python3
"""
Ultra-Fast Renko Builder
Optimized for <5ms processing using:
- NumPy vectorization
- Numba JIT compilation  
- Memory-efficient arrays
- Zero pandas overhead
"""

import numpy as np
import numba as nb
from numba import jit, njit
import array
from collections import deque
import time
import logging

# Pre-compile critical functions with Numba JIT
@njit(cache=True)
def calculate_brick_count(price_diff, brick_size):
    """Ultra-fast brick count calculation"""
    return int(abs(price_diff) / brick_size)

@njit(cache=True)
def create_brick_prices(start_price, brick_size, num_bricks, direction):
    """Vectorized brick price calculation"""
    if direction > 0:  # Up bricks
        prices = np.arange(num_bricks, dtype=np.float64)
        prices = start_price + (prices + 1) * brick_size
    else:  # Down bricks
        prices = np.arange(num_bricks, dtype=np.float64)
        prices = start_price - (prices + 1) * brick_size
    return prices

@njit(cache=True)
def fast_atr_calculation(highs, lows, closes, period):
    """Ultra-fast ATR calculation using Numba"""
    n = len(closes)
    if n < period + 1:
        return 0.0
    
    # Calculate True Range
    tr = np.zeros(n-1, dtype=np.float64)
    for i in range(1, n):
        tr[i-1] = max(
            highs[i] - lows[i],
            abs(highs[i] - closes[i-1]),
            abs(lows[i] - closes[i-1])
        )
    
    # Calculate ATR (simple moving average of TR)
    if len(tr) < period:
        return np.mean(tr)
    else:
        return np.mean(tr[-period:])

class UltraFastRenko:
    """Ultra-optimized Renko builder for <5ms processing"""
    
    def __init__(self, brick_size=0.1, max_history=1000):
        self.brick_size = brick_size
        self.max_history = max_history
        
        # Use NumPy arrays for maximum speed
        self.timestamps = array.array('d')  # Double precision timestamps
        self.opens = array.array('f')       # Float32 for prices (sufficient precision)
        self.highs = array.array('f')
        self.lows = array.array('f')
        self.closes = array.array('f')
        self.directions = array.array('b')  # Byte for direction (-1, 0, 1)
        
        # Current brick state (single values, not arrays)
        self.current_open = 0.0
        self.current_high = 0.0
        self.current_low = 0.0
        self.current_close = 0.0
        self.initialized = False
        
        # Performance tracking
        self.processing_times = deque(maxlen=100)
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Ultra-Fast Renko initialized: {brick_size} brick size")
    
    def add_tick_ultra_fast(self, timestamp, price):
        """
        Ultra-fast tick processing: <5ms target
        Returns: number of new bricks created
        """
        start_time = time.perf_counter()
        
        try:
            if not self.initialized:
                self._initialize_first_brick(timestamp, price)
                return 0
            
            # Calculate price difference (single operation)
            price_diff = price - self.current_close
            
            # Check if new brick(s) needed (single comparison)
            if abs(price_diff) < self.brick_size:
                # Update current brick high/low only
                self.current_high = max(self.current_high, price)
                self.current_low = min(self.current_low, price)
                return 0
            
            # Calculate number of bricks (JIT compiled)
            num_bricks = calculate_brick_count(price_diff, self.brick_size)
            direction = 1 if price_diff > 0 else -1
            
            # Create brick prices (vectorized)
            brick_closes = create_brick_prices(
                self.current_close, self.brick_size, num_bricks, direction
            )
            
            # Add bricks to arrays (batch operation)
            for i in range(num_bricks):
                brick_close = brick_closes[i]
                
                if direction > 0:  # Up brick
                    brick_open = brick_close - self.brick_size
                    brick_high = brick_close
                    brick_low = brick_open
                else:  # Down brick
                    brick_open = brick_close + self.brick_size
                    brick_high = brick_open
                    brick_low = brick_close
                
                # Append to arrays (C-speed)
                self.timestamps.append(timestamp)
                self.opens.append(brick_open)
                self.highs.append(brick_high)
                self.lows.append(brick_low)
                self.closes.append(brick_close)
                self.directions.append(direction)
                
                # Update current brick
                self.current_open = brick_open
                self.current_high = brick_high
                self.current_low = brick_low
                self.current_close = brick_close
            
            # Maintain max history (efficient)
            self._trim_history()
            
            return num_bricks
            
        finally:
            # Track processing time
            processing_time = (time.perf_counter() - start_time) * 1000
            self.processing_times.append(processing_time)
            
            if processing_time > 5.0:  # Warn if >5ms
                self.logger.warning(f"Slow Renko processing: {processing_time:.2f}ms")
    
    def _initialize_first_brick(self, timestamp, price):
        """Initialize first brick (called once)"""
        self.current_open = price
        self.current_high = price
        self.current_low = price
        self.current_close = price
        self.initialized = True
        
        self.logger.info(f"First brick initialized at {price:.5f}")
    
    def _trim_history(self):
        """Efficiently maintain max history"""
        if len(self.closes) > self.max_history:
            # Remove oldest 10% when limit reached
            trim_count = self.max_history // 10
            
            # Slice arrays (efficient)
            self.timestamps = array.array('d', self.timestamps[trim_count:])
            self.opens = array.array('f', self.opens[trim_count:])
            self.highs = array.array('f', self.highs[trim_count:])
            self.lows = array.array('f', self.lows[trim_count:])
            self.closes = array.array('f', self.closes[trim_count:])
            self.directions = array.array('b', self.directions[trim_count:])
    
    def get_numpy_arrays(self, last_n=None):
        """
        Get data as NumPy arrays (ultra-fast)
        Returns: dict of numpy arrays
        """
        if last_n and last_n < len(self.closes):
            start_idx = len(self.closes) - last_n
            return {
                'timestamps': np.array(self.timestamps[start_idx:], dtype=np.float64),
                'opens': np.array(self.opens[start_idx:], dtype=np.float32),
                'highs': np.array(self.highs[start_idx:], dtype=np.float32),
                'lows': np.array(self.lows[start_idx:], dtype=np.float32),
                'closes': np.array(self.closes[start_idx:], dtype=np.float32),
                'directions': np.array(self.directions[start_idx:], dtype=np.int8)
            }
        else:
            return {
                'timestamps': np.array(self.timestamps, dtype=np.float64),
                'opens': np.array(self.opens, dtype=np.float32),
                'highs': np.array(self.highs, dtype=np.float32),
                'lows': np.array(self.lows, dtype=np.float32),
                'closes': np.array(self.closes, dtype=np.float32),
                'directions': np.array(self.directions, dtype=np.int8)
            }
    
    def calculate_fast_atr(self, period=14):
        """Ultra-fast ATR calculation"""
        if len(self.closes) < period + 1:
            return 0.0
        
        # Get numpy arrays
        highs = np.array(self.highs[-period-1:], dtype=np.float64)
        lows = np.array(self.lows[-period-1:], dtype=np.float64)
        closes = np.array(self.closes[-period-1:], dtype=np.float64)
        
        # Use JIT-compiled ATR calculation
        return fast_atr_calculation(highs, lows, closes, period)
    
    def get_performance_stats(self):
        """Get processing performance statistics"""
        if not self.processing_times:
            return {}
        
        times = list(self.processing_times)
        return {
            'avg_processing_ms': np.mean(times),
            'max_processing_ms': np.max(times),
            'min_processing_ms': np.min(times),
            'total_bricks': len(self.closes),
            'under_5ms_pct': (np.sum(np.array(times) < 5.0) / len(times)) * 100
        }
    
    def reset(self):
        """Reset all data"""
        self.timestamps = array.array('d')
        self.opens = array.array('f')
        self.highs = array.array('f')
        self.lows = array.array('f')
        self.closes = array.array('f')
        self.directions = array.array('b')
        
        self.current_open = 0.0
        self.current_high = 0.0
        self.current_low = 0.0
        self.current_close = 0.0
        self.initialized = False
        
        self.processing_times.clear()
        self.logger.info("⚡ Ultra-Fast Renko reset")

# Performance test
if __name__ == "__main__":
    print("⚡ Testing Ultra-Fast Renko Builder...")
    
    # Create ultra-fast builder
    renko = UltraFastRenko(brick_size=0.1)
    
    # Performance test with 1000 ticks
    test_prices = np.random.normal(8555.0, 0.5, 1000)  # Random walk
    timestamps = np.arange(1748983549, 1748983549 + 1000)
    
    print(f"🧪 Processing {len(test_prices)} ticks...")
    
    start_time = time.perf_counter()
    total_bricks = 0
    
    for timestamp, price in zip(timestamps, test_prices):
        new_bricks = renko.add_tick_ultra_fast(timestamp, price)
        total_bricks += new_bricks
    
    total_time = (time.perf_counter() - start_time) * 1000
    
    # Get performance stats
    stats = renko.get_performance_stats()
    
    print(f"\n⚡ ULTRA-FAST PERFORMANCE RESULTS:")
    print(f"   Total time: {total_time:.2f}ms")
    print(f"   Avg per tick: {total_time/len(test_prices):.3f}ms")
    print(f"   Total bricks: {total_bricks}")
    print(f"   Avg processing: {stats['avg_processing_ms']:.3f}ms")
    print(f"   Max processing: {stats['max_processing_ms']:.3f}ms")
    print(f"   Under 5ms: {stats['under_5ms_pct']:.1f}%")
    
    print(f"\n✅ Ultra-Fast Renko test completed!")
