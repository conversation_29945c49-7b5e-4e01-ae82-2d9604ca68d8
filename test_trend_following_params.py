#!/usr/bin/env python3
"""
Test Different UT Bot Parameters for Trend Following
Test various combinations to find optimal trend-riding settings

Focus on making UT Bot ride trends instead of fighting them
"""

import pandas as pd
import sys
import os

# Add the qt-meta directory to the path
sys.path.append('qt-meta')
from ut_bot_adaptation import UTBot

def test_trend_parameters():
    """Test different parameter combinations for trend following"""
    print("🔧 Testing UT Bot Parameters for Trend Following")
    print("=" * 60)
    
    # Load our trending data (the +69.7 point bullish move)
    print("📖 Loading trending Step Index data...")
    df = pd.read_csv('step_index_renko_0_1.csv')
    df['datetime'] = pd.to_datetime(df['datetime'])
    print(f"✅ Loaded {len(df)} Renko bricks")
    print(f"📈 Price movement: {df['open'].iloc[0]:.1f} → {df['close'].iloc[-1]:.1f} (+{df['close'].iloc[-1] - df['open'].iloc[0]:.1f} points)")
    
    # Test different parameter combinations
    test_configs = [
        # Current (Reversal-focused)
        {"name": "Current (Reversal)", "atr_period": 1, "sensitivity": 1},
        
        # Trend-following variations
        {"name": "Trend-Light", "atr_period": 3, "sensitivity": 1.5},
        {"name": "Trend-Medium", "atr_period": 5, "sensitivity": 2.0},
        {"name": "Trend-Strong", "atr_period": 7, "sensitivity": 2.5},
        {"name": "Trend-Heavy", "atr_period": 10, "sensitivity": 3.0},
        
        # Extreme trend following
        {"name": "Trend-Extreme", "atr_period": 15, "sensitivity": 4.0},
    ]
    
    results = []
    
    for config in test_configs:
        print(f"\n🧪 Testing: {config['name']}")
        print(f"   ATR Period: {config['atr_period']}, Sensitivity: {config['sensitivity']}")
        
        # Initialize UT Bot with test parameters
        ut_bot = UTBot(atr_period=config['atr_period'], sensitivity=config['sensitivity'])
        
        # Generate signals
        signals_df = ut_bot.run(df)
        
        # Merge signals with price data
        merged_df = pd.concat([df, signals_df], axis=1)
        
        # Analyze signal quality for trending market
        buy_signals = merged_df['buy'].sum()
        sell_signals = merged_df['sell'].sum()
        
        # Calculate signal timing (early vs late in trend)
        first_buy_idx = merged_df[merged_df['buy'] == True].index[0] if buy_signals > 0 else None
        last_sell_idx = merged_df[merged_df['sell'] == True].index[-1] if sell_signals > 0 else None
        
        # Calculate potential profit if we rode the trend
        if first_buy_idx is not None:
            entry_price = merged_df.loc[first_buy_idx, 'close']
            exit_price = merged_df['close'].iloc[-1]  # Hold to end
            trend_profit = exit_price - entry_price
        else:
            trend_profit = 0
            entry_price = 0
        
        result = {
            'config': config['name'],
            'atr_period': config['atr_period'],
            'sensitivity': config['sensitivity'],
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'first_buy_idx': first_buy_idx,
            'entry_price': entry_price,
            'trend_profit': trend_profit,
            'signal_ratio': buy_signals / sell_signals if sell_signals > 0 else float('inf')
        }
        
        results.append(result)
        
        print(f"   📊 Buy signals: {buy_signals}, Sell signals: {sell_signals}")
        if first_buy_idx is not None:
            print(f"   📈 First buy at brick {first_buy_idx}: {entry_price:.1f}")
            print(f"   💰 Trend profit potential: +{trend_profit:.1f} points")
        else:
            print(f"   ❌ No buy signals generated")
    
    # Summary analysis
    print(f"\n📊 PARAMETER ANALYSIS SUMMARY")
    print("=" * 60)
    
    best_trend_config = max(results, key=lambda x: x['trend_profit'])
    
    for result in results:
        profit_str = f"+{result['trend_profit']:.1f}" if result['trend_profit'] > 0 else f"{result['trend_profit']:.1f}"
        ratio_str = f"{result['signal_ratio']:.1f}" if result['signal_ratio'] != float('inf') else "∞"
        
        marker = "🏆" if result == best_trend_config else "  "
        
        print(f"{marker} {result['config']:15s} | "
              f"ATR:{result['atr_period']:2d} Sens:{result['sensitivity']:3.1f} | "
              f"Buy:{result['buy_signals']:2d} Sell:{result['sell_signals']:2d} | "
              f"Ratio:{ratio_str:4s} | "
              f"Profit:{profit_str:6s}")
    
    print(f"\n🎯 RECOMMENDATIONS:")
    print(f"🏆 Best for trend following: {best_trend_config['config']}")
    print(f"   Parameters: ATR Period = {best_trend_config['atr_period']}, Sensitivity = {best_trend_config['sensitivity']}")
    print(f"   Trend profit potential: +{best_trend_config['trend_profit']:.1f} points")
    
    # Additional insights
    print(f"\n💡 INSIGHTS:")
    print(f"   • Higher ATR period = Smoother signals, less whipsaw")
    print(f"   • Higher sensitivity = Wider stops, let trends run longer")
    print(f"   • Buy/Sell ratio should favor buy signals in uptrend")
    print(f"   • Early entry (low brick index) captures more trend")
    
    return results, best_trend_config

def main():
    """Main function"""
    try:
        results, best_config = test_trend_parameters()
        
        print(f"\n🎉 PARAMETER TESTING COMPLETED!")
        print(f"📁 Use these settings for trend-following:")
        print(f"   ut_bot = UTBot(atr_period={best_config['atr_period']}, sensitivity={best_config['sensitivity']})")
        
    except Exception as e:
        print(f"\n💥 Testing error: {e}")

if __name__ == "__main__":
    main()
