#!/usr/bin/env python3
"""
Trading Configuration
All trading parameters and settings for live UT Bot system
"""

# MT5 Account Configuration
MT5_CONFIG = {
    'login': ********,
    'password': '@Ripper25',
    'server': 'Deriv-Demo',
    'symbol': 'Step Index'
}

# UT Bot Parameters (Proven from backtests)
UTBOT_CONFIG = {
    'atr_period': 1,        # Proven optimal
    'sensitivity': 1,       # Proven optimal
    'min_bricks': 10        # Minimum bricks needed for signal
}

# Renko Configuration
RENKO_CONFIG = {
    'brick_size': 0.1,      # 0.1 point bricks (proven)
    'max_history': 1000     # Keep last 1000 bricks in memory
}

# Risk Management
RISK_CONFIG = {
    'initial_lot_size': 0.01,      # Starting position size
    'max_lot_size': 1.0,           # Maximum position size
    'max_daily_loss': -100.0,      # Max $100 daily loss
    'position_sizing_method': 'dynamic',  # 'fixed' or 'dynamic'
    'risk_per_trade': 0.01         # 1% of account per trade
}

# Trading Hours (24/7 for Step Index)
TRADING_HOURS = {
    'enabled': True,        # Enable trading hours filter
    'start_hour': 0,        # 00:00 UTC
    'end_hour': 23,         # 23:59 UTC
    'timezone': 'UTC'
}

# Performance Monitoring
PERFORMANCE_CONFIG = {
    'log_level': 'INFO',           # DEBUG, INFO, WARNING, ERROR
    'save_trades_to_csv': True,    # Save all trades to CSV
    'save_renko_to_csv': True,     # Save Renko data to CSV
    'performance_update_interval': 60,  # Update stats every 60 seconds
    'max_log_file_size': 10        # Max log file size in MB
}

# System Performance
SYSTEM_CONFIG = {
    'tick_processing_timeout': 500,    # Max 500ms per tick
    'order_execution_timeout': 200,    # Max 200ms per order
    'connection_retry_attempts': 3,    # Retry connection 3 times
    'connection_retry_delay': 5,       # 5 seconds between retries
    'heartbeat_interval': 30           # Check connection every 30 seconds
}

# File Paths
FILE_PATHS = {
    'logs_dir': 'logs',
    'data_dir': 'data',
    'trades_file': 'logs/live_trades.csv',
    'renko_file': 'data/live_renko.csv',
    'performance_file': 'logs/performance.csv'
}

# Emergency Controls
EMERGENCY_CONFIG = {
    'enable_kill_switch': True,        # Enable emergency stop
    'max_consecutive_losses': 5,       # Stop after 5 consecutive losses
    'max_drawdown_percent': 20,        # Stop if 20% drawdown
    'emergency_close_all': True        # Close all positions on emergency
}

# Validation functions
def validate_config():
    """Validate all configuration parameters"""
    errors = []
    
    # Validate MT5 config
    if not MT5_CONFIG['login'] or not MT5_CONFIG['password']:
        errors.append("MT5 login credentials missing")
    
    # Validate UT Bot config
    if UTBOT_CONFIG['atr_period'] < 1:
        errors.append("ATR period must be >= 1")
    
    if UTBOT_CONFIG['sensitivity'] <= 0:
        errors.append("Sensitivity must be > 0")
    
    # Validate Renko config
    if RENKO_CONFIG['brick_size'] <= 0:
        errors.append("Brick size must be > 0")
    
    # Validate risk config
    if RISK_CONFIG['initial_lot_size'] <= 0:
        errors.append("Initial lot size must be > 0")
    
    if RISK_CONFIG['max_lot_size'] < RISK_CONFIG['initial_lot_size']:
        errors.append("Max lot size must be >= initial lot size")
    
    # Validate system config
    if SYSTEM_CONFIG['tick_processing_timeout'] > 1000:
        errors.append("Tick processing timeout too high (>1000ms)")
    
    if SYSTEM_CONFIG['order_execution_timeout'] > 500:
        errors.append("Order execution timeout too high (>500ms)")
    
    return errors

def get_config_summary():
    """Get a summary of all configuration settings"""
    return {
        'MT5 Account': f"{MT5_CONFIG['login']} on {MT5_CONFIG['server']}",
        'Symbol': MT5_CONFIG['symbol'],
        'UT Bot': f"ATR={UTBOT_CONFIG['atr_period']}, Sensitivity={UTBOT_CONFIG['sensitivity']}",
        'Renko': f"Brick size={RENKO_CONFIG['brick_size']}",
        'Risk': f"Initial lot={RISK_CONFIG['initial_lot_size']}, Max lot={RISK_CONFIG['max_lot_size']}",
        'Performance': f"Tick timeout={SYSTEM_CONFIG['tick_processing_timeout']}ms, Order timeout={SYSTEM_CONFIG['order_execution_timeout']}ms"
    }

# Test configuration
if __name__ == "__main__":
    print("⚙️ Testing Trading Configuration...")
    
    # Validate config
    errors = validate_config()
    
    if errors:
        print("❌ Configuration errors found:")
        for error in errors:
            print(f"   • {error}")
    else:
        print("✅ Configuration validation passed")
    
    # Show config summary
    print("\n📊 Configuration Summary:")
    summary = get_config_summary()
    for key, value in summary.items():
        print(f"   {key}: {value}")
    
    print("\n🚀 Trading configuration ready!")
