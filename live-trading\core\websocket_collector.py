#!/usr/bin/env python3
"""
Lightning-Fast WebSocket Tick Data Collector for Deriv.com
Optimized for ultra-low latency tick collection and processing

Features:
- High-performance async WebSocket connection
- Real-time tick data buffering
- Thread-safe data sharing
- Automatic reconnection
- Tick rate monitoring
- Memory-efficient circular buffers
"""

import asyncio
import websockets
import json
import logging
import time
import threading
from collections import deque
from datetime import datetime
from typing import Dict, List, Optional, Callable
import numpy as np

class HighPerformanceTickCollector:
    def __init__(self, app_id: str, symbols: List[str], buffer_size: int = 10000):
        self.app_id = app_id
        self.symbols = symbols
        self.buffer_size = buffer_size
        
        # High-performance data structures
        self.tick_buffers = {symbol: deque(maxlen=buffer_size) for symbol in symbols}
        self.latest_ticks = {symbol: None for symbol in symbols}
        self.tick_counts = {symbol: 0 for symbol in symbols}
        self.last_tick_times = {symbol: 0 for symbol in symbols}
        
        # Connection management
        self.websocket = None
        self.running = False
        self.connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10
        
        # Performance monitoring
        self.start_time = time.time()
        self.total_ticks_received = 0
        self.tick_rate_window = deque(maxlen=100)  # Last 100 tick timestamps
        
        # Thread safety
        self.data_lock = threading.RLock()
        self.callbacks = []
        
        # Logging
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        
    def add_tick_callback(self, callback: Callable):
        """Add callback function to be called on each tick"""
        self.callbacks.append(callback)
        
    def get_latest_tick(self, symbol: str) -> Optional[Dict]:
        """Get the latest tick for a symbol (thread-safe)"""
        with self.data_lock:
            return self.latest_ticks.get(symbol)
            
    def get_tick_buffer(self, symbol: str, count: int = None) -> List[Dict]:
        """Get recent ticks from buffer (thread-safe)"""
        with self.data_lock:
            buffer = self.tick_buffers.get(symbol, deque())
            if count is None:
                return list(buffer)
            else:
                return list(buffer)[-count:] if len(buffer) >= count else list(buffer)
                
    def get_tick_rate(self) -> float:
        """Calculate current tick rate (ticks per second)"""
        if len(self.tick_rate_window) < 2:
            return 0.0
        
        time_span = self.tick_rate_window[-1] - self.tick_rate_window[0]
        if time_span <= 0:
            return 0.0
            
        return (len(self.tick_rate_window) - 1) / time_span
        
    def get_statistics(self) -> Dict:
        """Get performance statistics"""
        uptime = time.time() - self.start_time
        avg_tick_rate = self.total_ticks_received / uptime if uptime > 0 else 0
        
        return {
            'uptime_seconds': uptime,
            'total_ticks': self.total_ticks_received,
            'average_tick_rate': avg_tick_rate,
            'current_tick_rate': self.get_tick_rate(),
            'connected': self.connected,
            'symbols': list(self.symbols),
            'buffer_sizes': {symbol: len(self.tick_buffers[symbol]) for symbol in self.symbols},
            'tick_counts': self.tick_counts.copy(),
            'reconnect_attempts': self.reconnect_attempts
        }
        
    async def connect(self) -> bool:
        """Establish WebSocket connection"""
        uri = f"wss://ws.derivws.com/websockets/v3?app_id={self.app_id}"
        
        try:
            self.logger.info(f"Connecting to Deriv WebSocket: {uri}")
            self.websocket = await websockets.connect(
                uri,
                ping_interval=30,
                ping_timeout=10,
                close_timeout=5,
                max_size=2**20,  # 1MB max message size
                compression=None  # Disable compression for speed
            )
            self.connected = True
            self.reconnect_attempts = 0
            self.logger.info("✅ WebSocket connected successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Connection failed: {e}")
            self.connected = False
            return False
            
    async def subscribe_to_symbols(self) -> bool:
        """Subscribe to tick streams for all symbols"""
        if not self.websocket or not self.connected:
            return False
            
        try:
            for symbol in self.symbols:
                subscribe_request = {
                    "ticks": symbol,
                    "subscribe": 1
                }
                await self.websocket.send(json.dumps(subscribe_request))
                self.logger.info(f"📡 Subscribed to {symbol}")
                
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Subscription failed: {e}")
            return False
            
    async def process_tick(self, tick_data: Dict):
        """Process incoming tick data with maximum efficiency"""
        try:
            symbol = tick_data['symbol']
            quote = float(tick_data['quote'])
            epoch = int(tick_data['epoch'])
            
            # Create optimized tick object
            tick = {
                'symbol': symbol,
                'price': quote,
                'timestamp': epoch,
                'datetime': datetime.fromtimestamp(epoch),
                'pip_size': tick_data.get('pip_size', 1)
            }
            
            # Thread-safe data update
            with self.data_lock:
                # Update latest tick
                self.latest_ticks[symbol] = tick
                
                # Add to buffer
                self.tick_buffers[symbol].append(tick)
                
                # Update counters
                self.tick_counts[symbol] += 1
                self.last_tick_times[symbol] = time.time()
                
                # Performance tracking
                self.total_ticks_received += 1
                self.tick_rate_window.append(time.time())
                
            # Execute callbacks (non-blocking)
            for callback in self.callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        asyncio.create_task(callback(tick))
                    else:
                        callback(tick)
                except Exception as e:
                    self.logger.error(f"Callback error: {e}")
                    
        except Exception as e:
            self.logger.error(f"❌ Tick processing error: {e}")
            
    async def message_handler(self):
        """Handle incoming WebSocket messages"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    
                    if 'tick' in data:
                        # High-priority tick processing
                        await self.process_tick(data['tick'])
                        
                    elif 'subscription' in data:
                        sub = data['subscription']
                        self.logger.info(f"✅ Subscription confirmed: {sub['id']}")
                        
                    elif 'error' in data:
                        error = data['error']
                        self.logger.error(f"❌ API Error: {error['message']} (Code: {error['code']})")
                        
                except json.JSONDecodeError as e:
                    self.logger.error(f"❌ JSON decode error: {e}")
                except Exception as e:
                    self.logger.error(f"❌ Message processing error: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("⚠️ WebSocket connection closed")
            self.connected = False
        except Exception as e:
            self.logger.error(f"❌ Message handler error: {e}")
            self.connected = False
            
    async def keep_alive(self):
        """Send periodic pings to maintain connection"""
        while self.running:
            try:
                if self.websocket and self.connected:
                    await self.websocket.send(json.dumps({"ping": 1}))
                    self.logger.debug("🏓 Ping sent")
                    
                await asyncio.sleep(30)  # Ping every 30 seconds
                
            except Exception as e:
                self.logger.error(f"❌ Keep-alive error: {e}")
                break
                
    async def reconnect_loop(self):
        """Handle automatic reconnection"""
        while self.running:
            if not self.connected and self.reconnect_attempts < self.max_reconnect_attempts:
                self.reconnect_attempts += 1
                self.logger.info(f"🔄 Reconnection attempt {self.reconnect_attempts}/{self.max_reconnect_attempts}")
                
                if await self.connect():
                    if await self.subscribe_to_symbols():
                        self.logger.info("✅ Reconnection successful")
                    else:
                        self.connected = False
                        
                if not self.connected:
                    wait_time = min(2 ** self.reconnect_attempts, 60)  # Exponential backoff
                    await asyncio.sleep(wait_time)
                    
            await asyncio.sleep(5)  # Check every 5 seconds
            
    async def start(self):
        """Start the tick collector"""
        self.running = True
        self.start_time = time.time()
        
        # Initial connection
        if not await self.connect():
            raise Exception("Failed to establish initial connection")
            
        if not await self.subscribe_to_symbols():
            raise Exception("Failed to subscribe to symbols")
            
        # Start concurrent tasks
        tasks = [
            asyncio.create_task(self.message_handler()),
            asyncio.create_task(self.keep_alive()),
            asyncio.create_task(self.reconnect_loop())
        ]
        
        self.logger.info("🚀 High-performance tick collector started")
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            self.logger.error(f"❌ Collector error: {e}")
        finally:
            await self.stop()
            
    async def stop(self):
        """Stop the tick collector"""
        self.logger.info("🛑 Stopping tick collector...")
        self.running = False
        self.connected = False
        
        if self.websocket:
            await self.websocket.close()
            
        self.logger.info("✅ Tick collector stopped")

# Example usage and testing
if __name__ == "__main__":
    async def tick_callback(tick):
        print(f"📊 {tick['symbol']}: {tick['price']} @ {tick['datetime']}")
        
    async def main():
        # Initialize collector
        collector = HighPerformanceTickCollector(
            app_id="71058",
            symbols=["stpRNG", "R_100", "R_50"],
            buffer_size=5000
        )
        
        # Add callback
        collector.add_tick_callback(tick_callback)
        
        # Start collecting
        await collector.start()
        
    # Run the collector
    asyncio.run(main())
