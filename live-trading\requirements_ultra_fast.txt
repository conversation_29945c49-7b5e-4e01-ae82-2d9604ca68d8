# Ultra-Low Latency Trading System Requirements
# Optimized for sub-500ms performance

# Core Performance Libraries
jax[cpu]==0.4.23                    # Google's ultra-fast numerical computing
jaxlib==0.4.23                      # JAX backend
numba==0.58.1                       # JIT compilation for Python
cupy-cuda12x==12.3.0                # GPU-accelerated NumPy (CUDA 12.x)
polars==0.20.3                      # Lightning-fast DataFrame operations

# Alternative High-Performance Options
taichi==1.7.0                       # Parallel computing framework
# torch==2.1.0                      # PyTorch (if using ML models)
# torchvision==0.16.0               # PyTorch vision (if needed)

# Optimized Data Structures
numpy==1.24.3                       # Optimized NumPy version
scipy==1.11.4                       # Scientific computing
pandas==2.1.4                       # For compatibility (use Polars when possible)

# Ultra-Fast Networking
uvloop==0.19.0                      # Ultra-fast asyncio event loop
websockets==12.0                    # High-performance WebSocket client
aiohttp==3.9.1                      # Async HTTP client
httpx==0.25.2                       # Modern HTTP client

# Low-Level Optimizations
cython==3.0.6                       # C extensions for Python
pyarrow==14.0.2                     # Columnar data format
orjson==3.9.10                      # Ultra-fast JSON parsing
msgpack==1.0.7                      # Binary serialization
lz4==4.3.2                          # Ultra-fast compression

# Memory Management
psutil==5.9.6                       # System monitoring
memory-profiler==0.61.0             # Memory usage tracking
pympler==0.9                        # Memory analysis

# MetaTrader 5 Integration
MetaTrader5==5.0.45                 # MT5 Python API

# Monitoring and Logging
structlog==23.2.0                   # Structured logging
prometheus-client==0.19.0           # Metrics collection
py-spy==0.3.14                      # Performance profiling

# Development and Testing
pytest==7.4.3                       # Testing framework
pytest-asyncio==0.21.1              # Async testing
pytest-benchmark==4.0.0             # Performance benchmarking
line-profiler==4.1.1                # Line-by-line profiling

# Optional: Machine Learning (if using ML models)
# xgboost==2.0.2                    # Gradient boosting (C++ backend)
# lightgbm==4.1.0                   # Microsoft's fast gradient boosting
# catboost==1.2.2                   # Yandex's gradient boosting

# System Dependencies (install via system package manager)
# CUDA Toolkit 12.x (for CuPy)
# Intel MKL (for optimized NumPy/SciPy)
# OpenBLAS (alternative to MKL)

# Performance Notes:
# 1. Use JAX for mathematical computations (ATR, signals)
# 2. Use Numba for loops and custom algorithms
# 3. Use CuPy for GPU acceleration (if available)
# 4. Use Polars instead of Pandas for data operations
# 5. Use uvloop for async operations
# 6. Use orjson for JSON parsing
# 7. Compile critical paths with Cython if needed
