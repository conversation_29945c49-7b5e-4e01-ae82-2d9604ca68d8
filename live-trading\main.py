#!/usr/bin/env python3
"""
Live Trading System Launcher
Lightning-fast UT Bot live trading with Deriv WebSocket and MT5 integration

Features:
- Real-time tick data collection
- Live UT Bot signal generation
- Automated trade execution
- Dynamic risk management
- Performance monitoring
- Error recovery
"""

import asyncio
import logging
import signal
import sys
import os
import time
from datetime import datetime
from typing import Dict, Optional

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.trading_config import create_config, LiveTradingConfig
from strategies.live_ut_bot import LiveUTBot
from core.websocket_collector import HighPerformanceTickCollector
from mt5.mt5_manager import MT5Manager
from utils.risk_manager import DynamicRiskManager

class LiveTradingSystem:
    def __init__(self, config: LiveTradingConfig):
        self.config = config
        self.running = False
        self.start_time = None
        
        # Core components
        self.ut_bot = None
        self.performance_monitor = None
        
        # Setup logging
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Performance tracking
        self.system_stats = {
            'start_time': None,
            'uptime': 0,
            'total_ticks': 0,
            'total_signals': 0,
            'total_trades': 0,
            'last_update': time.time()
        }
        
    def setup_logging(self):
        """Setup comprehensive logging system"""
        log_level = getattr(logging, self.config.performance.log_level.upper())
        
        # Create logs directory
        log_dir = os.path.join(os.path.dirname(__file__), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # Setup file handler
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(log_dir, f"live_trading_{timestamp}.log")
        
        # Configure logging
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # Reduce noise from external libraries
        logging.getLogger('websockets').setLevel(logging.WARNING)
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        
    async def initialize(self) -> bool:
        """Initialize the live trading system"""
        try:
            self.logger.info("🚀 Initializing Live Trading System...")
            self.logger.info(f"Environment: {self.config.environment}")
            self.logger.info(f"Symbol: {self.config.trading.symbol}")
            self.logger.info(f"Brick Size: {self.config.trading.brick_size}")
            
            # Validate configuration
            if not self.config.validate():
                raise Exception("Configuration validation failed")
                
            # Create UT Bot configuration
            bot_config = {
                'symbol': self.config.trading.symbol,
                'brick_size': self.config.trading.brick_size,
                'atr_period': self.config.trading.atr_period,
                'sensitivity': self.config.trading.sensitivity,
                'signal_cooldown': self.config.trading.signal_cooldown,
                'deriv_app_id': self.config.deriv.app_id,
                'mt5_login': self.config.mt5.login,
                'mt5_password': self.config.mt5.password,
                'mt5_server': self.config.mt5.server,
                'initial_balance': self.config.risk.initial_balance,
                'max_risk_per_trade': self.config.risk.max_risk_percent / 100,
                'min_risk_per_trade': self.config.risk.min_risk_percent / 100
            }
            
            # Initialize UT Bot
            self.ut_bot = LiveUTBot(bot_config)
            
            if not await self.ut_bot.initialize():
                raise Exception("Failed to initialize UT Bot")
                
            # Start performance monitoring
            if self.config.performance.enable_performance_monitoring:
                asyncio.create_task(self.performance_monitor_loop())
                
            self.logger.info("✅ Live Trading System initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Initialization failed: {e}")
            return False
            
    async def start(self):
        """Start the live trading system"""
        if not await self.initialize():
            raise Exception("Failed to initialize system")
            
        self.running = True
        self.start_time = time.time()
        self.system_stats['start_time'] = self.start_time
        
        self.logger.info("🎯 Starting live trading...")
        self.logger.info("Press Ctrl+C to stop")
        
        try:
            # Start the UT Bot
            await self.ut_bot.start()
            
        except KeyboardInterrupt:
            self.logger.info("🛑 Keyboard interrupt received")
        except Exception as e:
            self.logger.error(f"❌ Trading system error: {e}")
        finally:
            await self.stop()
            
    async def stop(self):
        """Stop the live trading system"""
        self.logger.info("🛑 Stopping live trading system...")
        self.running = False
        
        # Stop UT Bot
        if self.ut_bot:
            await self.ut_bot.stop()
            
        # Generate final report
        await self.generate_final_report()
        
        self.logger.info("✅ Live trading system stopped")
        
    async def performance_monitor_loop(self):
        """Monitor system performance"""
        while self.running:
            try:
                await asyncio.sleep(60)  # Update every minute
                await self.update_performance_stats()
                
            except Exception as e:
                self.logger.error(f"❌ Performance monitoring error: {e}")
                
    async def update_performance_stats(self):
        """Update system performance statistics"""
        try:
            current_time = time.time()
            self.system_stats['uptime'] = current_time - self.start_time
            self.system_stats['last_update'] = current_time
            
            # Get component statistics
            if self.ut_bot:
                bot_stats = self.ut_bot.get_performance_stats()
                self.system_stats.update({
                    'total_signals': bot_stats.get('signals_generated', 0),
                    'total_trades': bot_stats.get('trades_executed', 0),
                    'signal_rate': bot_stats.get('signal_rate', 0),
                    'trade_rate': bot_stats.get('trade_rate', 0)
                })
                
                # Get tick collector stats
                if self.ut_bot.tick_collector:
                    tick_stats = self.ut_bot.tick_collector.get_statistics()
                    self.system_stats['total_ticks'] = tick_stats.get('total_ticks', 0)
                    self.system_stats['tick_rate'] = tick_stats.get('current_tick_rate', 0)
                    
                # Get MT5 stats
                if self.ut_bot.mt5_manager:
                    mt5_stats = self.ut_bot.mt5_manager.get_performance_stats()
                    self.system_stats['mt5_orders'] = mt5_stats.get('total_orders', 0)
                    self.system_stats['mt5_success_rate'] = mt5_stats.get('success_rate_percent', 0)
                    
            # Log performance summary
            uptime_hours = self.system_stats['uptime'] / 3600
            self.logger.info(f"📊 Performance Update:")
            self.logger.info(f"  Uptime: {uptime_hours:.1f} hours")
            self.logger.info(f"  Ticks: {self.system_stats.get('total_ticks', 0)}")
            self.logger.info(f"  Signals: {self.system_stats.get('total_signals', 0)}")
            self.logger.info(f"  Trades: {self.system_stats.get('total_trades', 0)}")
            self.logger.info(f"  Tick Rate: {self.system_stats.get('tick_rate', 0):.1f}/sec")
            
        except Exception as e:
            self.logger.error(f"❌ Stats update error: {e}")
            
    async def generate_final_report(self):
        """Generate final performance report"""
        try:
            self.logger.info("📋 Generating final report...")
            
            uptime_hours = self.system_stats['uptime'] / 3600
            
            self.logger.info("=" * 60)
            self.logger.info("📊 FINAL PERFORMANCE REPORT")
            self.logger.info("=" * 60)
            self.logger.info(f"Trading Session Duration: {uptime_hours:.2f} hours")
            self.logger.info(f"Total Ticks Processed: {self.system_stats.get('total_ticks', 0):,}")
            self.logger.info(f"Total Signals Generated: {self.system_stats.get('total_signals', 0)}")
            self.logger.info(f"Total Trades Executed: {self.system_stats.get('total_trades', 0)}")
            
            if self.ut_bot and self.ut_bot.risk_manager:
                risk_metrics = self.ut_bot.risk_manager.get_risk_metrics()
                self.logger.info(f"Final Balance: ${risk_metrics.get('current_balance', 0):.2f}")
                self.logger.info(f"Win Rate: {risk_metrics.get('win_rate_percent', 0):.1f}%")
                self.logger.info(f"Profit Factor: {risk_metrics.get('profit_factor', 0):.2f}")
                self.logger.info(f"Max Drawdown: {risk_metrics.get('max_drawdown_percent', 0):.1f}%")
                
            if self.ut_bot and self.ut_bot.mt5_manager:
                mt5_stats = self.ut_bot.mt5_manager.get_performance_stats()
                self.logger.info(f"MT5 Order Success Rate: {mt5_stats.get('success_rate_percent', 0):.1f}%")
                self.logger.info(f"Average Execution Time: {mt5_stats.get('average_execution_time_ms', 0):.1f}ms")
                
            self.logger.info("=" * 60)
            
        except Exception as e:
            self.logger.error(f"❌ Report generation error: {e}")

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print("\n🛑 Shutdown signal received...")
    sys.exit(0)

async def main():
    """Main entry point"""
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Live Trading System")
    parser.add_argument("--env", choices=["development", "testing", "production"], 
                       default="development", help="Environment")
    parser.add_argument("--preset", choices=["conservative", "aggressive", "scalping"], 
                       help="Configuration preset")
    parser.add_argument("--symbol", help="Trading symbol")
    parser.add_argument("--brick-size", type=float, help="Renko brick size")
    parser.add_argument("--balance", type=float, help="Initial balance")
    
    args = parser.parse_args()
    
    # Create configuration
    config = create_config(args.env, args.preset)
    
    # Apply command line overrides
    if args.symbol:
        config.trading.symbol = args.symbol
    if args.brick_size:
        config.trading.brick_size = args.brick_size
    if args.balance:
        config.risk.initial_balance = args.balance
        
    # Create and start trading system
    system = LiveTradingSystem(config)
    
    try:
        await system.start()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ System error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
