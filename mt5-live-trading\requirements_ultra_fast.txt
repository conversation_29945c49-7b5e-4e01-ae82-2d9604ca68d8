# Ultra-Fast Trading System Requirements
# Core libraries for maximum performance

# Essential numerical computing
numpy>=1.24.0              # Vectorized operations (foundation)
numba>=0.58.0              # JIT compilation (100x speed boost)

# Core trading libraries  
MetaTrader5>=5.0.45        # MT5 API
pandas>=2.0.0              # Data handling (minimal usage)

# Ultra-fast alternatives (optional but recommended)
polars>=0.20.0             # 30x faster than pandas
pyarrow>=14.0.0            # Columnar data format

# GPU acceleration (optional - if CUDA available)
# cupy>=12.0.0             # GPU-accelerated NumPy
# cudf>=23.0.0             # GPU-accelerated DataFrame

# Memory optimization
psutil>=5.9.0              # Memory monitoring
memory-profiler>=0.61.0    # Memory usage tracking

# Parallel processing
concurrent-futures>=3.1.1  # Thread/process pools

# Development and testing
pytest>=7.0.0              # Testing framework
pytest-benchmark>=4.0.0    # Performance benchmarking

# Logging and monitoring
colorlog>=6.7.0            # Colored logging
rich>=13.0.0               # Rich terminal output

# Optional: Cython for even more speed
# Cython>=3.0.0            # C-speed Python extensions

# Installation notes:
# 1. Install with: pip install -r requirements_ultra_fast.txt
# 2. For GPU support: pip install cupy-cuda11x (if CUDA 11.x)
# 3. For Cython: pip install Cython && python setup.py build_ext --inplace
