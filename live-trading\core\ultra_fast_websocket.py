#!/usr/bin/env python3
"""
Ultra-Fast WebSocket Tick Collector
Optimized for sub-100ms latency using uvloop, orjson, and high-performance techniques

Performance Targets:
- Tick processing: <10ms
- JSON parsing: <1ms
- WebSocket latency: <50ms
"""

import asyncio
import uvloop  # Ultra-fast event loop
import websockets
import orjson  # Ultra-fast JSON parsing
import time
import logging
import threading
from collections import deque
from datetime import datetime
from typing import Dict, List, Optional, Callable, Deque
import numpy as np
from dataclasses import dataclass
import msgpack  # Binary serialization for internal use

# Set uvloop as the default event loop for maximum performance
asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())

@dataclass
class TickData:
    """Optimized tick data structure"""
    symbol: str
    price: float
    timestamp: float
    epoch: int
    pip_size: float = 1.0
    
    def to_dict(self) -> Dict:
        return {
            'symbol': self.symbol,
            'price': self.price,
            'timestamp': self.timestamp,
            'epoch': self.epoch,
            'pip_size': self.pip_size
        }

class UltraFastTickCollector:
    """Ultra-optimized WebSocket tick collector for maximum speed"""
    
    def __init__(self, app_id: str, symbols: List[str], buffer_size: int = 50000):
        self.app_id = app_id
        self.symbols = symbols
        self.buffer_size = buffer_size
        
        # Ultra-fast data structures
        self.tick_buffers: Dict[str, Deque[TickData]] = {
            symbol: deque(maxlen=buffer_size) for symbol in symbols
        }
        self.latest_ticks: Dict[str, Optional[TickData]] = {
            symbol: None for symbol in symbols
        }
        
        # Performance tracking with minimal overhead
        self.tick_counts = {symbol: 0 for symbol in symbols}
        self.processing_times = deque(maxlen=1000)  # Last 1000 processing times
        self.total_ticks_received = 0
        self.start_time = time.perf_counter()
        
        # Connection state
        self.websocket = None
        self.connected = False
        self.running = False
        
        # Callbacks (pre-allocated for speed)
        self.tick_callbacks: List[Callable] = []
        self.signal_callbacks: List[Callable] = []
        
        # Thread safety with minimal locking
        self.data_lock = threading.RLock()
        
        # Logging
        self.logger = logging.getLogger(__name__)
        
        # Pre-compiled message templates for speed
        self.subscribe_templates = {
            symbol: orjson.dumps({"ticks": symbol, "subscribe": 1})
            for symbol in symbols
        }
        self.ping_message = orjson.dumps({"ping": 1})
        
    def add_tick_callback(self, callback: Callable[[TickData], None]):
        """Add callback for tick data (optimized)"""
        self.tick_callbacks.append(callback)
        
    def add_signal_callback(self, callback: Callable[[Dict], None]):
        """Add callback for trading signals"""
        self.signal_callbacks.append(callback)
        
    async def connect(self) -> bool:
        """Establish ultra-fast WebSocket connection"""
        uri = f"wss://ws.derivws.com/websockets/v3?app_id={self.app_id}"
        
        try:
            self.logger.info(f"🚀 Connecting to ultra-fast WebSocket: {uri}")
            
            # Optimized connection parameters
            self.websocket = await websockets.connect(
                uri,
                ping_interval=None,  # Disable automatic pings for speed
                ping_timeout=None,
                close_timeout=1,
                max_size=2**16,  # 64KB max message size
                compression=None,  # Disable compression for speed
                max_queue=2**10,  # 1024 message queue
            )
            
            self.connected = True
            self.logger.info("✅ Ultra-fast WebSocket connected")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Connection failed: {e}")
            self.connected = False
            return False
            
    async def subscribe_to_symbols(self) -> bool:
        """Subscribe to all symbols with minimal latency"""
        if not self.websocket or not self.connected:
            return False
            
        try:
            # Send all subscriptions rapidly
            for symbol in self.symbols:
                await self.websocket.send(self.subscribe_templates[symbol])
                
            self.logger.info(f"📡 Subscribed to {len(self.symbols)} symbols")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Subscription failed: {e}")
            return False
            
    async def process_tick_ultra_fast(self, tick_data: Dict) -> TickData:
        """Ultra-optimized tick processing"""
        # Direct field access for maximum speed
        symbol = tick_data['symbol']
        price = tick_data['quote']
        epoch = tick_data['epoch']
        
        # Create optimized tick object
        tick = TickData(
            symbol=symbol,
            price=price,
            timestamp=time.perf_counter(),
            epoch=epoch,
            pip_size=tick_data.get('pip_size', 1.0)
        )
        
        # Ultra-fast buffer update (minimal locking)
        with self.data_lock:
            self.latest_ticks[symbol] = tick
            self.tick_buffers[symbol].append(tick)
            self.tick_counts[symbol] += 1
            self.total_ticks_received += 1
            
        return tick
        
    async def execute_callbacks(self, tick: TickData):
        """Execute callbacks with minimal overhead"""
        # Execute tick callbacks
        for callback in self.tick_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    # Don't await - fire and forget for speed
                    asyncio.create_task(callback(tick))
                else:
                    callback(tick)
            except Exception as e:
                # Log errors but don't stop processing
                self.logger.error(f"Callback error: {e}")
                
    async def message_handler_ultra_fast(self):
        """Ultra-optimized message handling"""
        try:
            async for raw_message in self.websocket:
                processing_start = time.perf_counter()
                
                try:
                    # Ultra-fast JSON parsing with orjson
                    data = orjson.loads(raw_message)
                    
                    # Fast message type detection
                    if 'tick' in data:
                        # High-priority tick processing
                        tick = await self.process_tick_ultra_fast(data['tick'])
                        
                        # Execute callbacks without blocking
                        asyncio.create_task(self.execute_callbacks(tick))
                        
                    elif 'subscription' in data:
                        # Log subscription confirmations
                        sub_id = data['subscription']['id']
                        self.logger.debug(f"✅ Subscription confirmed: {sub_id}")
                        
                    elif 'error' in data:
                        # Handle errors
                        error = data['error']
                        self.logger.error(f"❌ API Error: {error['message']}")
                        
                    # Track processing time
                    processing_time = (time.perf_counter() - processing_start) * 1000
                    self.processing_times.append(processing_time)
                    
                    # Warn if processing is slow
                    if processing_time > 10:  # >10ms is considered slow
                        self.logger.warning(f"Slow tick processing: {processing_time:.2f}ms")
                        
                except orjson.JSONDecodeError as e:
                    self.logger.error(f"❌ JSON decode error: {e}")
                except Exception as e:
                    self.logger.error(f"❌ Message processing error: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("⚠️ WebSocket connection closed")
            self.connected = False
        except Exception as e:
            self.logger.error(f"❌ Message handler error: {e}")
            self.connected = False
            
    async def keep_alive_ultra_fast(self):
        """Ultra-fast keep-alive with minimal overhead"""
        while self.running and self.connected:
            try:
                await asyncio.sleep(30)  # Ping every 30 seconds
                
                if self.websocket and not self.websocket.closed:
                    await self.websocket.send(self.ping_message)
                    
            except Exception as e:
                self.logger.error(f"❌ Keep-alive error: {e}")
                break
                
    def get_latest_tick(self, symbol: str) -> Optional[TickData]:
        """Get latest tick with minimal overhead"""
        return self.latest_ticks.get(symbol)
        
    def get_recent_ticks(self, symbol: str, count: int = 100) -> List[TickData]:
        """Get recent ticks efficiently"""
        with self.data_lock:
            buffer = self.tick_buffers.get(symbol, deque())
            if count >= len(buffer):
                return list(buffer)
            else:
                return list(buffer)[-count:]
                
    def get_performance_stats(self) -> Dict:
        """Get ultra-fast performance statistics"""
        uptime = time.perf_counter() - self.start_time
        
        with self.data_lock:
            recent_times = list(self.processing_times)[-100:]  # Last 100 processing times
            
            return {
                'uptime_seconds': uptime,
                'total_ticks': self.total_ticks_received,
                'tick_rate': self.total_ticks_received / uptime if uptime > 0 else 0,
                'average_processing_time_ms': np.mean(recent_times) if recent_times else 0,
                'min_processing_time_ms': np.min(recent_times) if recent_times else 0,
                'max_processing_time_ms': np.max(recent_times) if recent_times else 0,
                'connected': self.connected,
                'symbols': self.symbols,
                'tick_counts': self.tick_counts.copy(),
                'buffer_utilization': {
                    symbol: len(self.tick_buffers[symbol]) / self.buffer_size 
                    for symbol in self.symbols
                }
            }
            
    async def start(self):
        """Start ultra-fast tick collection"""
        self.running = True
        self.start_time = time.perf_counter()
        
        # Connect with retries
        max_retries = 3
        for attempt in range(max_retries):
            if await self.connect():
                break
            if attempt < max_retries - 1:
                await asyncio.sleep(1)
            else:
                raise Exception("Failed to establish connection after retries")
                
        # Subscribe to symbols
        if not await self.subscribe_to_symbols():
            raise Exception("Failed to subscribe to symbols")
            
        # Start concurrent tasks
        tasks = [
            asyncio.create_task(self.message_handler_ultra_fast()),
            asyncio.create_task(self.keep_alive_ultra_fast())
        ]
        
        self.logger.info("🚀 Ultra-fast tick collector started")
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            self.logger.error(f"❌ Collector error: {e}")
        finally:
            await self.stop()
            
    async def stop(self):
        """Stop ultra-fast tick collection"""
        self.logger.info("🛑 Stopping ultra-fast tick collector...")
        self.running = False
        self.connected = False
        
        if self.websocket:
            await self.websocket.close()
            
        # Final performance report
        stats = self.get_performance_stats()
        self.logger.info(f"📊 Final Stats: {stats['total_ticks']} ticks, "
                        f"{stats['tick_rate']:.1f} ticks/sec, "
                        f"{stats['average_processing_time_ms']:.2f}ms avg processing")
        
        self.logger.info("✅ Ultra-fast tick collector stopped")

# Performance testing
async def benchmark_ultra_fast_collector():
    """Benchmark the ultra-fast collector"""
    print("🚀 Benchmarking Ultra-Fast WebSocket Collector...")
    
    collector = UltraFastTickCollector(
        app_id="71058",
        symbols=["stpRNG"],
        buffer_size=10000
    )
    
    # Track performance
    tick_count = 0
    start_time = time.perf_counter()
    
    def tick_callback(tick: TickData):
        nonlocal tick_count
        tick_count += 1
        if tick_count % 100 == 0:
            elapsed = time.perf_counter() - start_time
            rate = tick_count / elapsed
            print(f"📊 Processed {tick_count} ticks, rate: {rate:.1f} ticks/sec")
            
    collector.add_tick_callback(tick_callback)
    
    try:
        # Run for 30 seconds
        await asyncio.wait_for(collector.start(), timeout=30)
    except asyncio.TimeoutError:
        await collector.stop()
        
    # Final stats
    stats = collector.get_performance_stats()
    print(f"✅ Benchmark complete:")
    print(f"  Total ticks: {stats['total_ticks']}")
    print(f"  Average processing: {stats['average_processing_time_ms']:.2f}ms")
    print(f"  Max processing: {stats['max_processing_time_ms']:.2f}ms")
    print(f"  Target <10ms: {'✅' if stats['average_processing_time_ms'] < 10 else '❌'}")

if __name__ == "__main__":
    asyncio.run(benchmark_ultra_fast_collector())
