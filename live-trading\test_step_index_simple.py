#!/usr/bin/env python3
"""
Simple Step Index Tick Data Streaming Test
Just test tick data streaming for Step Index symbol

Account: ********
Server: Deriv-Demo
Password: @Ripper25
"""

import MetaTrader5 as mt5
import time
from datetime import datetime

def stream_step_index_ticks():
    """Stream tick data for Step Index"""
    print("📈 Step Index Tick Data Streaming Test")
    print("=" * 50)

    # Initialize MT5
    if not mt5.initialize():
        print(f"❌ MT5 initialization failed: {mt5.last_error()}")
        return False

    print("✅ MT5 initialized")

    # Login
    login = ********
    password = "@Ripper25"
    server = "Deriv-Demo"

    print(f"🔐 Logging in to {login} on {server}...")

    if not mt5.login(login, password, server):
        print(f"❌ Login failed: {mt5.last_error()}")
        mt5.shutdown()
        return False

    print("✅ Login successful")

    # Find Step Index symbol
    step_index_names = [
        "Step Index",
        "STEP Index",
        "Step_Index",
        "STEP_Index",
        "stpRNG",
        "Volatility 100 Index",
        "R_100"
    ]

    print("\n🔍 Finding Step Index symbol...")

    found_symbol = None

    for symbol_name in step_index_names:
        test_info = mt5.symbol_info(symbol_name)
        if test_info is not None:
            found_symbol = symbol_name
            print(f"✅ Found Step Index as: {symbol_name}")
            break

    if not found_symbol:
        print("❌ Step Index symbol not found!")
        mt5.shutdown()
        return False

    # Stream tick data
    print(f"\n📊 Streaming tick data for {found_symbol}...")
    print("Press Ctrl+C to stop")
    print("-" * 80)

    tick_count = 0
    start_time = time.time()

    try:
        while True:
            tick = mt5.symbol_info_tick(found_symbol)

            if tick:
                tick_count += 1
                current_time = datetime.fromtimestamp(tick.time)

                print(f"Tick #{tick_count:4d} | "
                      f"Time: {current_time.strftime('%H:%M:%S')} | "
                      f"Bid: {tick.bid:8.5f} | "
                      f"Ask: {tick.ask:8.5f} | "
                      f"Spread: {(tick.ask - tick.bid):6.5f}")

                # Show rate every 10 ticks
                if tick_count % 10 == 0:
                    elapsed = time.time() - start_time
                    rate = tick_count / elapsed if elapsed > 0 else 0
                    print(f"📈 Rate: {rate:.1f} ticks/second")

            else:
                print("❌ Failed to get tick data")

            time.sleep(0.1)  # 100ms delay between requests

    except KeyboardInterrupt:
        print(f"\n🛑 Streaming stopped by user")

    # Show final stats
    elapsed = time.time() - start_time
    rate = tick_count / elapsed if elapsed > 0 else 0

    print(f"\n� Final Statistics:")
    print(f"  Total ticks: {tick_count}")
    print(f"  Duration: {elapsed:.1f} seconds")
    print(f"  Average rate: {rate:.1f} ticks/second")

    # Cleanup
    mt5.shutdown()
    print("✅ Test completed")

    return True

def main():
    """Main test function"""
    try:
        success = stream_step_index_ticks()

        if success:
            print("\n🎯 STEP INDEX TICK STREAMING TEST PASSED!")
            print("Ready for UT Bot integration")
        else:
            print("\n❌ STEP INDEX TICK STREAMING TEST FAILED!")
            print("Check MT5 connection and symbol availability")

    except Exception as e:
        print(f"\n💥 Test error: {e}")
        print("Ensure MT5 is installed and running")

if __name__ == "__main__":
    main()
