#!/usr/bin/env python3
"""
Simple Step Index Test Script
Test Step Index symbol detection, tick data, and basic trading in MT5

Account: ********
Server: Deriv-Demo
Password: @Ripper25
"""

import MetaTrader5 as mt5
import time
from datetime import datetime

def test_step_index():
    """Test Step Index symbol in MT5"""
    print("🧪 Step Index Symbol Test")
    print("=" * 50)
    
    # Initialize MT5
    if not mt5.initialize():
        print(f"❌ MT5 initialization failed: {mt5.last_error()}")
        return False
    
    print("✅ MT5 initialized")
    
    # Login
    login = ********
    password = "@Ripper25"
    server = "Deriv-Demo"
    
    print(f"🔐 Logging in to {login} on {server}...")
    
    if not mt5.login(login, password, server):
        print(f"❌ Login failed: {mt5.last_error()}")
        mt5.shutdown()
        return False
    
    print("✅ Login successful")
    
    # Test different Step Index symbol names
    step_index_names = [
        "Step Index",
        "STEP Index",
        "Step_Index", 
        "STEP_Index",
        "stpRNG",
        "Volatility 100 Index",
        "R_100",
        "Volatility_100_Index"
    ]
    
    print("\n🔍 Searching for Step Index symbol...")
    
    found_symbol = None
    symbol_info = None
    
    for symbol_name in step_index_names:
        print(f"  Testing: {symbol_name}")
        test_info = mt5.symbol_info(symbol_name)
        
        if test_info is not None:
            found_symbol = symbol_name
            symbol_info = test_info
            print(f"  ✅ FOUND: {symbol_name}")
            break
        else:
            print(f"  ❌ Not found: {symbol_name}")
    
    if not found_symbol:
        print("\n❌ Step Index symbol not found with any name!")
        print("\n📋 Available symbols (first 50):")
        symbols = mt5.symbols_get()
        if symbols:
            for i, sym in enumerate(symbols[:50]):
                print(f"  {i+1:2d}. {sym.name} - {sym.description}")
        mt5.shutdown()
        return False
    
    print(f"\n✅ Step Index found as: {found_symbol}")
    
    # Display symbol information
    print(f"\n📊 Symbol Information:")
    print(f"  Name: {symbol_info.name}")
    print(f"  Description: {symbol_info.description}")
    print(f"  Currency: {symbol_info.currency_base}")
    print(f"  Digits: {symbol_info.digits}")
    print(f"  Point: {symbol_info.point}")
    print(f"  Min Volume: {symbol_info.volume_min}")
    print(f"  Max Volume: {symbol_info.volume_max}")
    print(f"  Volume Step: {symbol_info.volume_step}")
    print(f"  Trade Mode: {symbol_info.trade_mode}")
    print(f"  Spread: {symbol_info.spread}")
    
    # Test tick data
    print(f"\n📈 Testing tick data for {found_symbol}...")
    
    for i in range(5):
        tick = mt5.symbol_info_tick(found_symbol)
        if tick:
            print(f"  Tick {i+1}: Time={datetime.fromtimestamp(tick.time)}, "
                  f"Bid={tick.bid}, Ask={tick.ask}, Volume={tick.volume}")
        else:
            print(f"  ❌ Failed to get tick {i+1}")
        
        time.sleep(1)  # Wait 1 second between ticks
    
    # Test if trading is allowed
    print(f"\n💼 Trading Information:")
    print(f"  Trade Mode: {symbol_info.trade_mode}")
    
    if symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_FULL:
        print("  ✅ Full trading allowed")
        
        # Test a small order
        print(f"\n⚡ Testing small order...")
        
        # Get current price
        tick = mt5.symbol_info_tick(found_symbol)
        if not tick:
            print("  ❌ Cannot get current price")
        else:
            # Prepare buy order
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": found_symbol,
                "volume": symbol_info.volume_min,
                "type": mt5.ORDER_TYPE_BUY,
                "price": tick.ask,
                "deviation": 20,
                "magic": 234000,
                "comment": "Step_Index_Test",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }
            
            print(f"  Order details:")
            print(f"    Symbol: {found_symbol}")
            print(f"    Volume: {symbol_info.volume_min}")
            print(f"    Price: {tick.ask}")
            print(f"    Type: BUY")
            
            # Check order first
            check_result = mt5.order_check(request)
            if check_result is None:
                print(f"  ❌ Order check failed: {mt5.last_error()}")
            else:
                print(f"  ✅ Order check passed")
                print(f"    Margin: {check_result.margin}")
                print(f"    Profit: {check_result.profit}")
                
                # Send order
                print(f"  🚀 Sending order...")
                result = mt5.order_send(request)
                
                if result is None:
                    print(f"  ❌ Order send failed: {mt5.last_error()}")
                elif result.retcode == mt5.TRADE_RETCODE_DONE:
                    print(f"  ✅ Order successful!")
                    print(f"    Order: {result.order}")
                    print(f"    Deal: {result.deal}")
                    print(f"    Volume: {result.volume}")
                    print(f"    Price: {result.price}")
                    
                    # Close the order immediately
                    print(f"  🔄 Closing test position...")
                    
                    close_request = {
                        "action": mt5.TRADE_ACTION_DEAL,
                        "symbol": found_symbol,
                        "volume": result.volume,
                        "type": mt5.ORDER_TYPE_SELL,
                        "position": result.order,
                        "price": mt5.symbol_info_tick(found_symbol).bid,
                        "deviation": 20,
                        "magic": 234000,
                        "comment": "Close_Test",
                        "type_time": mt5.ORDER_TIME_GTC,
                        "type_filling": mt5.ORDER_FILLING_IOC,
                    }
                    
                    close_result = mt5.order_send(close_request)
                    if close_result and close_result.retcode == mt5.TRADE_RETCODE_DONE:
                        print(f"  ✅ Test position closed successfully")
                    else:
                        print(f"  ⚠️ Failed to close test position: {close_result.comment if close_result else 'Unknown error'}")
                        
                else:
                    print(f"  ❌ Order failed: {result.comment}")
                    
    elif symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_CLOSEONLY:
        print("  ⚠️ Close only mode - new positions not allowed")
    elif symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_DISABLED:
        print("  ❌ Trading disabled for this symbol")
    else:
        print(f"  ❓ Unknown trade mode: {symbol_info.trade_mode}")
    
    # Get account info
    print(f"\n💰 Account Information:")
    account_info = mt5.account_info()
    if account_info:
        print(f"  Balance: ${account_info.balance:.2f}")
        print(f"  Equity: ${account_info.equity:.2f}")
        print(f"  Margin: ${account_info.margin:.2f}")
        print(f"  Free Margin: ${account_info.margin_free:.2f}")
    
    # Check current positions
    print(f"\n📋 Current Positions:")
    positions = mt5.positions_get()
    if positions:
        for pos in positions:
            print(f"  {pos.symbol}: {pos.type_str} {pos.volume} @ {pos.price_open}")
    else:
        print("  No open positions")
    
    # Cleanup
    mt5.shutdown()
    
    print(f"\n🎉 Step Index test completed!")
    print(f"✅ Symbol found: {found_symbol}")
    print(f"✅ Tick data working")
    print(f"✅ Trading {'enabled' if symbol_info.trade_mode == mt5.SYMBOL_TRADE_MODE_FULL else 'limited'}")
    
    return True

def main():
    """Main test function"""
    try:
        success = test_step_index()
        
        if success:
            print("\n🎯 STEP INDEX TEST PASSED!")
            print("Ready for UT Bot integration")
        else:
            print("\n❌ STEP INDEX TEST FAILED!")
            print("Check MT5 connection and symbol availability")
            
    except Exception as e:
        print(f"\n💥 Test error: {e}")
        print("Ensure MT5 is installed and running")

if __name__ == "__main__":
    main()
