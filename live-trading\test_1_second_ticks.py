#!/usr/bin/env python3
"""
Simple 1-Second Tick Data Test for Step Index
Test real 1-second tick intervals from MT5

Account: ********
Server: Deriv-Demo
Password: @Ripper25
"""

import MetaTrader5 as mt5
import time
from datetime import datetime

def test_1_second_ticks():
    """Test 1-second tick data for Step Index"""
    print("⏱️ Step Index 1-Second Tick Test")
    print("=" * 50)
    
    # Initialize MT5
    if not mt5.initialize():
        print(f"❌ MT5 initialization failed: {mt5.last_error()}")
        return False
    
    print("✅ MT5 initialized")
    
    # Login
    login = ********
    password = "@Ripper25"
    server = "Deriv-Demo"
    
    print(f"🔐 Logging in to {login} on {server}...")
    
    if not mt5.login(login, password, server):
        print(f"❌ Login failed: {mt5.last_error()}")
        mt5.shutdown()
        return False
    
    print("✅ Login successful")
    
    # Find Step Index symbol
    step_index_names = [
        "Step Index",
        "STEP Index",
        "Step_Index", 
        "STEP_Index"
    ]
    
    print("\n🔍 Finding Step Index symbol...")
    
    found_symbol = None
    
    for symbol_name in step_index_names:
        test_info = mt5.symbol_info(symbol_name)
        if test_info is not None:
            found_symbol = symbol_name
            print(f"✅ Found Step Index as: {symbol_name}")
            break
    
    if not found_symbol:
        print("❌ Step Index symbol not found!")
        mt5.shutdown()
        return False
    
    # Test 1-second tick intervals
    print(f"\n📊 Testing 1-second tick intervals for {found_symbol}...")
    print("Collecting 30 ticks (1 per second)")
    print("-" * 80)
    
    tick_count = 0
    last_tick_time = None
    
    try:
        for i in range(30):  # Collect 30 ticks
            tick = mt5.symbol_info_tick(found_symbol)
            
            if tick:
                current_time = datetime.fromtimestamp(tick.time)
                
                # Check if this is a new tick (different timestamp)
                if last_tick_time != tick.time:
                    tick_count += 1
                    last_tick_time = tick.time
                    
                    print(f"Tick #{tick_count:2d} | "
                          f"Time: {current_time.strftime('%H:%M:%S')} | "
                          f"Bid: {tick.bid:8.5f} | "
                          f"Ask: {tick.ask:8.5f}")
                else:
                    print(f"         | Same tick - waiting for new data...")
                    
            else:
                print("❌ Failed to get tick data")
                
            time.sleep(1.0)  # Wait exactly 1 second
            
    except KeyboardInterrupt:
        print(f"\n🛑 Test stopped by user")
    
    print(f"\n📊 Test Results:")
    print(f"  Unique ticks collected: {tick_count}")
    print(f"  Expected: 30 ticks (1 per second)")
    print(f"  Success rate: {(tick_count/30)*100:.1f}%")
    
    # Cleanup
    mt5.shutdown()
    print("✅ Test completed")
    
    return True

def main():
    """Main test function"""
    try:
        success = test_1_second_ticks()
        
        if success:
            print("\n🎯 1-SECOND TICK TEST COMPLETED!")
        else:
            print("\n❌ 1-SECOND TICK TEST FAILED!")
            
    except Exception as e:
        print(f"\n💥 Test error: {e}")

if __name__ == "__main__":
    main()
