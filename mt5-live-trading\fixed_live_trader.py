#!/usr/bin/env python3
"""
FIXED LIVE UT BOT TRADING SYSTEM
Addresses ALL issues:

1. Uses EXACT ut_bot_adaptation.py technology
2. Implements EXACT ut_bot_backtest.py reversal mechanisms  
3. Sends REAL FOK orders to MT5 terminal
4. No simulations - REAL MONEY TRADING

⚠️  WARNING: THIS TRADES REAL MONEY WITH REAL ORDERS ⚠️
"""

import sys
import os
import time
import logging
import signal
import pandas as pd
from datetime import datetime

# Add core modules to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
sys.path.append(os.path.join(os.path.dirname(__file__), 'config'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'qt-meta'))

# Import EXACT modules from proven system
from mt5_connection import MT5Connection
from renko_builder import <PERSON>TimeR<PERSON>Builder
from ut_bot_adaptation import UTBot  # EXACT same UT Bot from backtest
from live_trading_strategy import LiveTradingStrategy
from order_manager import OrderManager
from trading_config import *

class FixedLiveTrader:
    """Fixed live trading system with EXACT backtest logic"""
    
    def __init__(self):
        # System components
        self.mt5 = None
        self.renko = None
        self.ut_bot = None  # EXACT same UT Bot from backtest
        self.strategy = None  # Live strategy with reversal mechanisms
        self.order_manager = None
        
        # Trading state
        self.running = False
        self.last_tick_time = None
        self.tick_count = 0
        self.signal_count = 0
        self.last_signal = None
        
        # Setup logging
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # Signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def setup_logging(self):
        """Setup logging for live trading"""
        os.makedirs('logs', exist_ok=True)
        
        log_format = '%(asctime)s | %(levelname)s | %(message)s'
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=[
                logging.FileHandler('logs/fixed_live_trading.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def initialize_system(self):
        """Initialize FIXED trading system"""
        try:
            self.logger.info("INITIALIZING FIXED LIVE TRADING SYSTEM")
            self.logger.info("Using EXACT ut_bot_adaptation.py technology")
            self.logger.info("Using EXACT ut_bot_backtest.py reversal mechanisms")
            self.logger.info("Sending REAL FOK orders to MT5")
            self.logger.info("=" * 60)
            
            # Initialize MT5 connection
            self.logger.info("Connecting to MT5...")
            self.mt5 = MT5Connection(
                login=MT5_CONFIG['login'],
                password=MT5_CONFIG['password'],
                server=MT5_CONFIG['server']
            )
            
            if not self.mt5.connect() or not self.mt5.find_step_index_symbol():
                return False
            
            # Initialize Renko builder
            self.logger.info("Initializing Renko builder...")
            self.renko = RealTimeRenkoBuilder(
                brick_size=RENKO_CONFIG['brick_size'],
                max_history=RENKO_CONFIG['max_history']
            )
            
            # Initialize EXACT UT Bot from backtest
            self.logger.info("Initializing EXACT UT Bot from ut_bot_adaptation.py...")
            self.ut_bot = UTBot(
                atr_period=UTBOT_CONFIG['atr_period'],
                sensitivity=UTBOT_CONFIG['sensitivity']
            )
            
            # Initialize order manager with FOK orders
            self.logger.info("Initializing Order Manager with FOK orders...")
            self.order_manager = OrderManager(
                mt5_connection=self.mt5,
                initial_lot_size=RISK_CONFIG['initial_lot_size'],
                max_lot_size=RISK_CONFIG['max_lot_size']
            )
            
            # Initialize live trading strategy with reversal mechanisms
            self.logger.info("Initializing Live Strategy with reversal mechanisms...")
            self.strategy = LiveTradingStrategy(self.order_manager)
            
            self.logger.info("FIXED SYSTEM READY!")
            self.logger.info("REAL MONEY TRADING WITH REAL ORDERS!")
            self.logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Fixed system initialization failed: {e}")
            return False
    
    def process_tick(self):
        """Process tick with EXACT backtest logic"""
        try:
            # Get new tick
            tick = self.mt5.get_tick()
            if not tick or self.last_tick_time == tick['time']:
                return False
            
            self.last_tick_time = tick['time']
            self.tick_count += 1
            
            # Add tick to Renko builder
            new_bricks = self.renko.add_tick(tick['time'], tick['bid'])
            
            if new_bricks:
                self.logger.info(f"New Renko brick(s): {len(new_bricks)} at {tick['bid']:.5f}")
                
                # Get Renko DataFrame (EXACT format for UT Bot)
                renko_df = self.renko.get_renko_dataframe(last_n=100)
                
                if len(renko_df) >= UTBOT_CONFIG['min_bricks']:
                    # Generate signals using EXACT UT Bot from backtest
                    signals_df = self.ut_bot.run(renko_df)
                    
                    # Get latest signals
                    latest_idx = len(signals_df) - 1
                    latest_buy = signals_df['buy'].iloc[latest_idx]
                    latest_sell = signals_df['sell'].iloc[latest_idx]
                    
                    # Create signal object
                    signal = {
                        'timestamp': datetime.now(),
                        'price': tick['bid'],
                        'buy_signal': bool(latest_buy),
                        'sell_signal': bool(latest_sell),
                        'signal_type': 'NONE'
                    }
                    
                    # Determine signal type (EXACT logic from backtest)
                    if latest_buy and not latest_sell:
                        signal['signal_type'] = 'BUY'
                    elif latest_sell and not latest_buy:
                        signal['signal_type'] = 'SELL'
                    
                    # Process signal if new
                    if self.is_new_signal(signal):
                        self.signal_count += 1
                        self.logger.info(f"NEW {signal['signal_type']} SIGNAL #{self.signal_count}")
                        
                        # Get account info
                        account_info = self.mt5.get_account_info()
                        
                        if account_info:
                            # Process signal with EXACT strategy logic
                            success = self.strategy.process_signal(
                                signal, tick['bid'], datetime.now(), account_info
                            )
                            
                            if success:
                                self.logger.info(f"REAL {signal['signal_type']} ORDER SENT TO MT5!")
                            else:
                                self.logger.error("Failed to send real order")
                        
                        self.last_signal = signal
                
                # Update active trades with EXACT reversal logic
                if new_bricks and self.strategy.active_trades:
                    latest_brick = renko_df.iloc[-1]
                    self.strategy.update_trades(
                        latest_brick['close'],
                        latest_brick['direction'],
                        datetime.now()
                    )
            
            # Log stats every 60 ticks
            if self.tick_count % 60 == 0:
                self.log_trading_stats()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error processing tick: {e}")
            return False
    
    def is_new_signal(self, signal):
        """Check if this is a new signal"""
        if signal['signal_type'] == 'NONE':
            return False
        
        if not self.last_signal:
            return True
        
        # Check if signal type changed
        if signal['signal_type'] != self.last_signal['signal_type']:
            return True
        
        return False
    
    def log_trading_stats(self):
        """Log trading statistics"""
        try:
            # Get account info
            account_info = self.mt5.get_account_info()
            
            # Get strategy summary
            strategy_summary = self.strategy.get_strategy_summary()
            
            self.logger.info("LIVE TRADING STATS")
            self.logger.info(f"   Ticks processed: {self.tick_count}")
            self.logger.info(f"   Signals generated: {self.signal_count}")
            self.logger.info(f"   Total trades: {strategy_summary['total_trades']}")
            self.logger.info(f"   Active trades: {strategy_summary['active_trades']}")
            self.logger.info(f"   Total profit: ${strategy_summary['total_profit']:.2f}")
            
            if account_info:
                self.logger.info(f"   Account balance: ${account_info['balance']:.2f}")
                self.logger.info(f"   Account equity: ${account_info['equity']:.2f}")
                self.logger.info(f"   Current profit: ${account_info['profit']:.2f}")
            
        except Exception as e:
            self.logger.error(f"Error logging stats: {e}")
    
    def run(self):
        """Main trading loop"""
        try:
            self.logger.info("STARTING FIXED LIVE TRADING")
            self.logger.info("USING EXACT BACKTEST TECHNOLOGY")
            self.logger.info("SENDING REAL FOK ORDERS")
            self.logger.info("Press Ctrl+C to stop")
            self.logger.info("-" * 60)
            
            self.running = True
            
            while self.running:
                # Process tick
                self.process_tick()
                
                # Small delay
                time.sleep(0.1)
            
        except KeyboardInterrupt:
            self.logger.info("Fixed live trading stopped by user")
        except Exception as e:
            self.logger.error(f"Fixed live trading error: {e}")
        finally:
            self.shutdown()
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Signal {signum} - shutting down...")
        self.running = False
    
    def shutdown(self):
        """Graceful shutdown"""
        try:
            self.logger.info("Shutting down fixed live trading...")
            
            # Close any open positions
            if self.strategy and self.strategy.active_trades:
                self.logger.info("Closing active trades...")
                for trade_id, trade in self.strategy.active_trades.items():
                    if trade.ticket:
                        self.mt5.close_position(trade.ticket)
                        self.logger.info(f"Closed trade {trade_id}")
            
            # Final stats
            self.log_trading_stats()
            
            # Disconnect
            if self.mt5:
                self.mt5.disconnect()
            
            self.logger.info("Fixed live trading shutdown completed")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")

def main():
    """Main entry point"""
    print("FIXED LIVE UT BOT TRADING SYSTEM")
    print("EXACT ut_bot_adaptation.py + ut_bot_backtest.py")
    print("REAL FOK ORDERS - NO SIMULATIONS")
    print("WARNING: THIS TRADES REAL MONEY!")
    print("=" * 50)
    
    # Create fixed trader
    trader = FixedLiveTrader()
    
    if trader.initialize_system():
        trader.run()
    else:
        print("Failed to initialize fixed system")

if __name__ == "__main__":
    main()
