#!/usr/bin/env python3
"""
Real-time Renko Builder
Convert live tick data to 0.1 Renko bricks for UT Bot processing

Ultra-fast processing: <20ms per tick
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging
from collections import deque

class RealTimeRenkoBuilder:
    """Build Renko charts from live tick data"""
    
    def __init__(self, brick_size=0.1, max_history=1000):
        self.brick_size = brick_size
        self.max_history = max_history
        
        # Current brick state
        self.current_brick = None
        self.last_price = None
        
        # Renko history (fast deque for performance)
        self.renko_history = deque(maxlen=max_history)
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"🧱 Renko Builder initialized: {brick_size} brick size")
    
    def add_tick(self, timestamp, price):
        """
        Add new tick and check if new Renko brick(s) formed
        Returns: List of new bricks (empty if no new bricks)
        """
        try:
            if self.current_brick is None:
                # Initialize first brick
                self._initialize_first_brick(timestamp, price)
                return []
            
            # Check if price movement creates new brick(s)
            price_diff = price - self.current_brick['close']
            
            if abs(price_diff) >= self.brick_size:
                # Calculate number of bricks to create
                num_bricks = int(abs(price_diff) / self.brick_size)
                direction = 'up' if price_diff > 0 else 'down'
                
                new_bricks = []
                
                for i in range(num_bricks):
                    new_brick = self._create_brick(timestamp, direction)
                    new_bricks.append(new_brick)
                    
                    # Add to history
                    self.renko_history.append(new_brick)
                    
                    # Update current brick for next iteration
                    self.current_brick = new_brick
                
                self.logger.debug(f"🧱 Created {num_bricks} {direction} brick(s) at {price:.5f}")
                return new_bricks
            
            else:
                # Update current brick high/low but don't close it
                self.current_brick['high'] = max(self.current_brick['high'], price)
                self.current_brick['low'] = min(self.current_brick['low'], price)
                return []
                
        except Exception as e:
            self.logger.error(f"Error adding tick: {e}")
            return []
    
    def _initialize_first_brick(self, timestamp, price):
        """Initialize the first brick"""
        self.current_brick = {
            'timestamp': timestamp,
            'datetime': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
            'open': price,
            'high': price,
            'low': price,
            'close': price,
            'direction': 'none'
        }
        self.last_price = price
        self.logger.info(f"🧱 First brick initialized at {price:.5f}")
    
    def _create_brick(self, timestamp, direction):
        """Create a new Renko brick"""
        if direction == 'up':
            # Up brick
            brick_open = self.current_brick['close']
            brick_close = brick_open + self.brick_size
            new_brick = {
                'timestamp': timestamp,
                'datetime': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                'open': round(brick_open, 1),
                'high': round(brick_close, 1),
                'low': round(brick_open, 1),
                'close': round(brick_close, 1),
                'direction': 'up'
            }
        else:
            # Down brick
            brick_open = self.current_brick['close']
            brick_close = brick_open - self.brick_size
            new_brick = {
                'timestamp': timestamp,
                'datetime': datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S'),
                'open': round(brick_open, 1),
                'high': round(brick_open, 1),
                'low': round(brick_close, 1),
                'close': round(brick_close, 1),
                'direction': 'down'
            }
        
        return new_brick
    
    def get_renko_dataframe(self, last_n=None):
        """
        Get Renko data as pandas DataFrame
        Args:
            last_n: Number of recent bricks to return (None = all)
        """
        try:
            if not self.renko_history:
                return pd.DataFrame()
            
            # Convert deque to list
            if last_n:
                data = list(self.renko_history)[-last_n:]
            else:
                data = list(self.renko_history)
            
            # Create DataFrame
            df = pd.DataFrame(data)
            df['datetime'] = pd.to_datetime(df['datetime'])
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error creating DataFrame: {e}")
            return pd.DataFrame()
    
    def get_latest_bricks(self, count=10):
        """Get the latest N bricks as list"""
        try:
            if count >= len(self.renko_history):
                return list(self.renko_history)
            else:
                return list(self.renko_history)[-count:]
        except Exception as e:
            self.logger.error(f"Error getting latest bricks: {e}")
            return []
    
    def get_current_price_info(self):
        """Get current price and brick information"""
        if not self.current_brick:
            return None
        
        return {
            'current_brick': self.current_brick.copy(),
            'total_bricks': len(self.renko_history),
            'last_direction': self.renko_history[-1]['direction'] if self.renko_history else 'none'
        }
    
    def save_to_csv(self, filename):
        """Save Renko history to CSV file"""
        try:
            df = self.get_renko_dataframe()
            if not df.empty:
                df.to_csv(filename, index=False)
                self.logger.info(f"💾 Saved {len(df)} Renko bricks to {filename}")
                return True
            else:
                self.logger.warning("No Renko data to save")
                return False
        except Exception as e:
            self.logger.error(f"Error saving to CSV: {e}")
            return False
    
    def reset(self):
        """Reset the Renko builder"""
        self.current_brick = None
        self.last_price = None
        self.renko_history.clear()
        self.logger.info("🔄 Renko builder reset")

# Test the Renko builder
if __name__ == "__main__":
    print("🧱 Testing Real-time Renko Builder...")
    
    # Create builder
    renko = RealTimeRenkoBuilder(brick_size=0.1)
    
    # Simulate some price movements
    test_prices = [
        (1748983549, 8554.5),  # Start
        (1748983550, 8554.6),  # +0.1 (1 up brick)
        (1748983551, 8554.7),  # +0.1 (1 up brick)
        (1748983552, 8554.9),  # +0.2 (2 up bricks)
        (1748983553, 8554.7),  # -0.2 (2 down bricks)
        (1748983554, 8554.8),  # +0.1 (1 up brick)
    ]
    
    total_bricks = 0
    
    for timestamp, price in test_prices:
        new_bricks = renko.add_tick(timestamp, price)
        total_bricks += len(new_bricks)
        
        if new_bricks:
            print(f"📊 Price {price:.1f} created {len(new_bricks)} brick(s)")
            for brick in new_bricks:
                print(f"   🧱 {brick['direction']}: {brick['open']:.1f} → {brick['close']:.1f}")
    
    # Get final DataFrame
    df = renko.get_renko_dataframe()
    print(f"\n📈 Final Renko DataFrame:")
    print(df[['datetime', 'open', 'close', 'direction']].tail())
    
    print(f"\n✅ Renko Builder test completed!")
    print(f"📊 Total bricks created: {total_bricks}")
    print(f"📊 Bricks in history: {len(renko.renko_history)}")
