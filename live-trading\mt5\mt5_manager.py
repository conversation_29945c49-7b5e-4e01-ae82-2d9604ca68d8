#!/usr/bin/env python3
"""
Lightning-Fast MetaTrader 5 API Manager
Optimized for ultra-low latency order execution and account management

Features:
- High-speed order execution
- Real-time position monitoring
- Account state management
- Risk management integration
- Error handling and recovery
- Performance monitoring
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
import logging
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class OrderType(Enum):
    BUY = mt5.ORDER_TYPE_BUY
    SELL = mt5.ORDER_TYPE_SELL
    BUY_LIMIT = mt5.ORDER_TYPE_BUY_LIMIT
    SELL_LIMIT = mt5.ORDER_TYPE_SELL_LIMIT
    BUY_STOP = mt5.ORDER_TYPE_BUY_STOP
    SELL_STOP = mt5.ORDER_TYPE_SELL_STOP

@dataclass
class TradeRequest:
    symbol: str
    action: str  # 'BUY' or 'SELL'
    volume: float
    price: Optional[float] = None
    sl: Optional[float] = None
    tp: Optional[float] = None
    deviation: int = 20
    magic: int = 234000
    comment: str = "UT_Bot_Live"
    type_time: int = mt5.ORDER_TIME_GTC

@dataclass
class TradeResult:
    success: bool
    order_id: Optional[int] = None
    deal_id: Optional[int] = None
    position_id: Optional[int] = None
    volume: Optional[float] = None
    price: Optional[float] = None
    error_code: Optional[int] = None
    error_description: Optional[str] = None
    execution_time_ms: Optional[float] = None

class MT5Manager:
    def __init__(self, login: int = None, password: str = None, server: str = None):
        self.login = login
        self.password = password
        self.server = server
        self.connected = False
        self.account_info = None
        
        # Performance tracking
        self.order_count = 0
        self.successful_orders = 0
        self.failed_orders = 0
        self.total_execution_time = 0.0
        self.last_order_time = 0
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Logging
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.INFO)
        
        # Symbol information cache
        self.symbol_info_cache = {}
        self.symbol_cache_time = {}
        self.cache_timeout = 300  # 5 minutes
        
    def initialize(self) -> bool:
        """Initialize MT5 connection"""
        try:
            # Initialize MT5
            if not mt5.initialize():
                self.logger.error(f"❌ MT5 initialization failed: {mt5.last_error()}")
                return False
                
            self.logger.info("✅ MT5 initialized successfully")
            
            # Login if credentials provided
            if self.login and self.password and self.server:
                if not mt5.login(self.login, self.password, self.server):
                    self.logger.error(f"❌ MT5 login failed: {mt5.last_error()}")
                    return False
                self.logger.info(f"✅ Logged in to account {self.login}")
                
            self.connected = True
            self.update_account_info()
            return True
            
        except Exception as e:
            self.logger.error(f"❌ MT5 initialization error: {e}")
            return False
            
    def shutdown(self):
        """Shutdown MT5 connection"""
        try:
            mt5.shutdown()
            self.connected = False
            self.logger.info("✅ MT5 connection closed")
        except Exception as e:
            self.logger.error(f"❌ MT5 shutdown error: {e}")
            
    def update_account_info(self) -> Dict:
        """Update and return account information"""
        try:
            self.account_info = mt5.account_info()._asdict() if mt5.account_info() else None
            return self.account_info
        except Exception as e:
            self.logger.error(f"❌ Failed to get account info: {e}")
            return {}
            
    def get_symbol_info(self, symbol: str, force_refresh: bool = False) -> Optional[Dict]:
        """Get symbol information with caching"""
        current_time = time.time()
        
        # Check cache
        if not force_refresh and symbol in self.symbol_info_cache:
            if current_time - self.symbol_cache_time.get(symbol, 0) < self.cache_timeout:
                return self.symbol_info_cache[symbol]
                
        try:
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info:
                info_dict = symbol_info._asdict()
                self.symbol_info_cache[symbol] = info_dict
                self.symbol_cache_time[symbol] = current_time
                return info_dict
            else:
                self.logger.warning(f"⚠️ Symbol {symbol} not found")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Failed to get symbol info for {symbol}: {e}")
            return None
            
    def calculate_lot_size(self, symbol: str, risk_amount: float, stop_loss_pips: float) -> float:
        """Calculate optimal lot size based on risk amount and stop loss"""
        try:
            symbol_info = self.get_symbol_info(symbol)
            if not symbol_info:
                return 0.0
                
            # Get symbol specifications
            tick_value = symbol_info['trade_tick_value']
            tick_size = symbol_info['trade_tick_size']
            min_lot = symbol_info['volume_min']
            max_lot = symbol_info['volume_max']
            lot_step = symbol_info['volume_step']
            
            # Calculate pip value
            pip_value = tick_value * (0.0001 / tick_size) if tick_size > 0 else 0
            
            # Calculate lot size
            if pip_value > 0 and stop_loss_pips > 0:
                lot_size = risk_amount / (stop_loss_pips * pip_value)
                
                # Round to lot step
                lot_size = round(lot_size / lot_step) * lot_step
                
                # Apply limits
                lot_size = max(min_lot, min(lot_size, max_lot))
                
                return lot_size
            else:
                return min_lot
                
        except Exception as e:
            self.logger.error(f"❌ Lot size calculation error: {e}")
            return 0.0
            
    def send_order(self, trade_request: TradeRequest) -> TradeResult:
        """Send order with lightning-fast execution"""
        start_time = time.time()
        
        with self.lock:
            self.order_count += 1
            
        try:
            # Get symbol info
            symbol_info = self.get_symbol_info(trade_request.symbol)
            if not symbol_info:
                return TradeResult(
                    success=False,
                    error_description=f"Symbol {trade_request.symbol} not found"
                )
                
            # Prepare order request
            if trade_request.action.upper() == 'BUY':
                order_type = mt5.ORDER_TYPE_BUY
                price = mt5.symbol_info_tick(trade_request.symbol).ask if not trade_request.price else trade_request.price
            else:
                order_type = mt5.ORDER_TYPE_SELL
                price = mt5.symbol_info_tick(trade_request.symbol).bid if not trade_request.price else trade_request.price
                
            # Build request
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": trade_request.symbol,
                "volume": trade_request.volume,
                "type": order_type,
                "price": price,
                "deviation": trade_request.deviation,
                "magic": trade_request.magic,
                "comment": trade_request.comment,
                "type_time": trade_request.type_time,
                "type_filling": mt5.ORDER_FILLING_IOC,  # Immediate or Cancel for speed
            }
            
            # Add SL/TP if provided
            if trade_request.sl:
                request["sl"] = trade_request.sl
            if trade_request.tp:
                request["tp"] = trade_request.tp
                
            # Send order
            result = mt5.order_send(request)
            execution_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            with self.lock:
                self.total_execution_time += execution_time
                self.last_order_time = time.time()
                
            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                with self.lock:
                    self.successful_orders += 1
                    
                self.logger.info(f"✅ Order executed: {trade_request.action} {trade_request.volume} {trade_request.symbol} @ {result.price} in {execution_time:.1f}ms")
                
                return TradeResult(
                    success=True,
                    order_id=result.order,
                    deal_id=result.deal,
                    volume=result.volume,
                    price=result.price,
                    execution_time_ms=execution_time
                )
            else:
                with self.lock:
                    self.failed_orders += 1
                    
                error_desc = f"Order failed: {result.comment if result else 'Unknown error'}"
                self.logger.error(f"❌ {error_desc}")
                
                return TradeResult(
                    success=False,
                    error_code=result.retcode if result else -1,
                    error_description=error_desc,
                    execution_time_ms=execution_time
                )
                
        except Exception as e:
            with self.lock:
                self.failed_orders += 1
                
            execution_time = (time.time() - start_time) * 1000
            error_msg = f"Order execution error: {e}"
            self.logger.error(f"❌ {error_msg}")
            
            return TradeResult(
                success=False,
                error_description=error_msg,
                execution_time_ms=execution_time
            )
            
    def close_position(self, symbol: str, position_id: int = None) -> TradeResult:
        """Close position(s) for a symbol"""
        try:
            positions = mt5.positions_get(symbol=symbol)
            if not positions:
                return TradeResult(success=False, error_description="No positions found")
                
            # Close specific position or all positions for symbol
            target_positions = [pos for pos in positions if position_id is None or pos.identifier == position_id]
            
            if not target_positions:
                return TradeResult(success=False, error_description="Target position not found")
                
            # Close positions
            results = []
            for position in target_positions:
                # Determine close order type
                if position.type == mt5.ORDER_TYPE_BUY:
                    order_type = mt5.ORDER_TYPE_SELL
                    price = mt5.symbol_info_tick(symbol).bid
                else:
                    order_type = mt5.ORDER_TYPE_BUY
                    price = mt5.symbol_info_tick(symbol).ask
                    
                # Close request
                close_request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": symbol,
                    "volume": position.volume,
                    "type": order_type,
                    "position": position.identifier,
                    "price": price,
                    "deviation": 20,
                    "magic": position.magic,
                    "comment": "Close by UT_Bot",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_IOC,
                }
                
                result = mt5.order_send(close_request)
                results.append(result)
                
            # Return result of last close operation
            last_result = results[-1] if results else None
            if last_result and last_result.retcode == mt5.TRADE_RETCODE_DONE:
                self.logger.info(f"✅ Position closed: {symbol}")
                return TradeResult(success=True, deal_id=last_result.deal)
            else:
                error_desc = f"Close failed: {last_result.comment if last_result else 'Unknown error'}"
                self.logger.error(f"❌ {error_desc}")
                return TradeResult(success=False, error_description=error_desc)
                
        except Exception as e:
            error_msg = f"Position close error: {e}"
            self.logger.error(f"❌ {error_msg}")
            return TradeResult(success=False, error_description=error_msg)
            
    def get_positions(self, symbol: str = None) -> List[Dict]:
        """Get current positions"""
        try:
            positions = mt5.positions_get(symbol=symbol) if symbol else mt5.positions_get()
            return [pos._asdict() for pos in positions] if positions else []
        except Exception as e:
            self.logger.error(f"❌ Failed to get positions: {e}")
            return []
            
    def get_performance_stats(self) -> Dict:
        """Get performance statistics"""
        with self.lock:
            avg_execution_time = (self.total_execution_time / self.order_count) if self.order_count > 0 else 0
            success_rate = (self.successful_orders / self.order_count * 100) if self.order_count > 0 else 0
            
            return {
                'total_orders': self.order_count,
                'successful_orders': self.successful_orders,
                'failed_orders': self.failed_orders,
                'success_rate_percent': success_rate,
                'average_execution_time_ms': avg_execution_time,
                'last_order_timestamp': self.last_order_time,
                'connected': self.connected
            }

# Example usage
if __name__ == "__main__":
    # Initialize MT5 manager
    mt5_manager = MT5Manager()
    
    if mt5_manager.initialize():
        print("✅ MT5 Manager initialized")
        
        # Get account info
        account = mt5_manager.update_account_info()
        print(f"Account Balance: ${account.get('balance', 0):.2f}")
        
        # Example trade
        trade = TradeRequest(
            symbol="EURUSD",
            action="BUY",
            volume=0.01,
            sl=1.1000,
            tp=1.1100
        )
        
        result = mt5_manager.send_order(trade)
        print(f"Trade Result: {result}")
        
        # Get performance stats
        stats = mt5_manager.get_performance_stats()
        print(f"Performance: {stats}")
        
        mt5_manager.shutdown()
    else:
        print("❌ Failed to initialize MT5 Manager")
