#!/usr/bin/env python3
"""
Enhanced Logging Utilities
Advanced logging for live trading system
"""

import logging
import os
from datetime import datetime
import csv

class TradingLogger:
    """Enhanced logger for trading operations"""
    
    def __init__(self, log_dir='logs'):
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)
        
        # Setup trade logger
        self.trade_logger = self._setup_trade_logger()
        
    def _setup_trade_logger(self):
        """Setup dedicated trade logging"""
        logger = logging.getLogger('trades')
        logger.setLevel(logging.INFO)
        
        # Trade log file
        trade_file = os.path.join(self.log_dir, 'trades.log')
        handler = logging.FileHandler(trade_file)
        
        formatter = logging.Formatter(
            '%(asctime)s | %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
    
    def log_trade(self, trade_data):
        """Log trade execution"""
        self.trade_logger.info(
            f"TRADE | {trade_data['type']} | "
            f"Price: {trade_data['price']:.5f} | "
            f"Volume: {trade_data['volume']} | "
            f"Ticket: {trade_data.get('ticket', 'N/A')}"
        )
    
    def save_trade_to_csv(self, trade_data, csv_file='logs/trades.csv'):
        """Save trade to CSV file"""
        try:
            file_exists = os.path.exists(csv_file)
            
            with open(csv_file, 'a', newline='') as f:
                fieldnames = ['timestamp', 'type', 'price', 'volume', 'ticket', 'profit']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                
                if not file_exists:
                    writer.writeheader()
                
                writer.writerow({
                    'timestamp': datetime.now().isoformat(),
                    'type': trade_data['type'],
                    'price': trade_data['price'],
                    'volume': trade_data['volume'],
                    'ticket': trade_data.get('ticket', ''),
                    'profit': trade_data.get('profit', 0)
                })
                
        except Exception as e:
            logging.error(f"Error saving trade to CSV: {e}")
