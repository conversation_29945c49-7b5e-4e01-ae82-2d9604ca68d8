#!/usr/bin/env python3
"""
Enhanced UT Bot with Trend Detection
Add trend filters to make UT Bot ride trends instead of fighting them

Key Enhancements:
1. Trend Detection (EMA crossovers, price momentum)
2. Trend-Aware Signal Filtering
3. Dynamic Parameter Adjustment
4. Early Trend Entry Logic
"""

import pandas as pd
import numpy as np
import sys
import os

# Add the qt-meta directory to the path
sys.path.append('qt-meta')
from ut_bot_adaptation import UTBot

class EnhancedTrendUTBot:
    """Enhanced UT Bot with trend detection and filtering"""
    
    def __init__(self, atr_period=1, sensitivity=1, trend_ema_fast=5, trend_ema_slow=20, min_trend_strength=0.5):
        self.ut_bot = UTBot(atr_period=atr_period, sensitivity=sensitivity)
        self.trend_ema_fast = trend_ema_fast
        self.trend_ema_slow = trend_ema_slow
        self.min_trend_strength = min_trend_strength
        
    def detect_trend(self, df):
        """Detect trend direction and strength"""
        # Calculate EMAs for trend detection
        df['ema_fast'] = df['close'].ewm(span=self.trend_ema_fast).mean()
        df['ema_slow'] = df['close'].ewm(span=self.trend_ema_slow).mean()
        
        # Trend direction: 1 = uptrend, -1 = downtrend, 0 = sideways
        df['trend_direction'] = np.where(df['ema_fast'] > df['ema_slow'], 1, -1)
        
        # Trend strength: distance between EMAs relative to price
        df['trend_strength'] = abs(df['ema_fast'] - df['ema_slow']) / df['close']
        
        # Strong trend filter
        df['strong_trend'] = df['trend_strength'] > self.min_trend_strength
        
        # Price momentum (rate of change)
        df['momentum'] = df['close'].pct_change(periods=5).fillna(0)
        
        # Trend confirmation: price above/below both EMAs
        df['uptrend_confirmed'] = (df['close'] > df['ema_fast']) & (df['close'] > df['ema_slow'])
        df['downtrend_confirmed'] = (df['close'] < df['ema_fast']) & (df['close'] < df['ema_slow'])
        
        return df
    
    def filter_signals_by_trend(self, df, signals_df):
        """Filter UT Bot signals based on trend direction"""
        # Merge signals with trend data
        merged_df = pd.concat([df, signals_df], axis=1)
        
        # Original signals
        original_buy = merged_df['buy'].copy()
        original_sell = merged_df['sell'].copy()
        
        # Trend-filtered signals
        # Only take BUY signals in confirmed uptrends
        merged_df['buy_filtered'] = (
            merged_df['buy'] & 
            merged_df['uptrend_confirmed'] & 
            (merged_df['momentum'] > 0)
        )
        
        # Only take SELL signals in confirmed downtrends
        merged_df['sell_filtered'] = (
            merged_df['sell'] & 
            merged_df['downtrend_confirmed'] & 
            (merged_df['momentum'] < 0)
        )
        
        # Early trend entry signals (when trend just starts)
        merged_df['early_buy'] = (
            (merged_df['ema_fast'] > merged_df['ema_slow']) &
            (merged_df['ema_fast'].shift(1) <= merged_df['ema_slow'].shift(1)) &  # EMA crossover
            (merged_df['close'] > merged_df['ema_fast'])
        )
        
        merged_df['early_sell'] = (
            (merged_df['ema_fast'] < merged_df['ema_slow']) &
            (merged_df['ema_fast'].shift(1) >= merged_df['ema_slow'].shift(1)) &  # EMA crossover
            (merged_df['close'] < merged_df['ema_fast'])
        )
        
        # Combined signals: Early entry OR filtered UT Bot signals
        merged_df['final_buy'] = merged_df['early_buy'] | merged_df['buy_filtered']
        merged_df['final_sell'] = merged_df['early_sell'] | merged_df['sell_filtered']
        
        return merged_df
    
    def run_enhanced(self, df):
        """Run enhanced UT Bot with trend filtering"""
        # Step 1: Detect trends
        df_with_trend = self.detect_trend(df.copy())
        
        # Step 2: Generate UT Bot signals
        signals_df = self.ut_bot.run(df)
        
        # Step 3: Filter signals by trend
        enhanced_df = self.filter_signals_by_trend(df_with_trend, signals_df)
        
        return enhanced_df

def test_enhanced_utbot():
    """Test the enhanced UT Bot on trending data"""
    print("🚀 Testing Enhanced Trend-Following UT Bot")
    print("=" * 60)
    
    # Load 7-day stpRNG data
    print("📖 Loading 7-day stpRNG Renko data...")
    df = pd.read_csv('qt-meta/stpRNG_7days_renko_0_1.csv')
    df['datetime'] = pd.to_datetime(df['datetime'])
    print(f"✅ Loaded {len(df)} Renko bricks")
    print(f"📈 Price movement: {df['open'].iloc[0]:.1f} → {df['close'].iloc[-1]:.1f} ({df['close'].iloc[-1] - df['open'].iloc[0]:+.1f} points)")
    
    # Test different configurations
    configs = [
        {"name": "Original UT Bot", "enhanced": False},
        {"name": "Enhanced Trend-Light", "enhanced": True, "trend_ema_fast": 3, "trend_ema_slow": 10, "min_trend_strength": 0.001},
        {"name": "Enhanced Trend-Medium", "enhanced": True, "trend_ema_fast": 5, "trend_ema_slow": 15, "min_trend_strength": 0.002},
        {"name": "Enhanced Trend-Strong", "enhanced": True, "trend_ema_fast": 5, "trend_ema_slow": 20, "min_trend_strength": 0.003},
    ]
    
    results = []
    
    for config in configs:
        print(f"\n🧪 Testing: {config['name']}")
        
        if config['enhanced']:
            # Enhanced UT Bot
            enhanced_bot = EnhancedTrendUTBot(
                atr_period=1, 
                sensitivity=1,
                trend_ema_fast=config['trend_ema_fast'],
                trend_ema_slow=config['trend_ema_slow'],
                min_trend_strength=config['min_trend_strength']
            )
            result_df = enhanced_bot.run_enhanced(df)
            
            # Use enhanced signals
            buy_signals = result_df['final_buy'].sum()
            sell_signals = result_df['final_sell'].sum()
            early_buy_signals = result_df['early_buy'].sum()
            
            # Find first signal
            first_buy_idx = result_df[result_df['final_buy'] == True].index[0] if buy_signals > 0 else None
            
        else:
            # Original UT Bot
            ut_bot = UTBot(atr_period=1, sensitivity=1)
            signals_df = ut_bot.run(df)
            result_df = pd.concat([df, signals_df], axis=1)
            
            buy_signals = result_df['buy'].sum()
            sell_signals = result_df['sell'].sum()
            early_buy_signals = 0
            
            first_buy_idx = result_df[result_df['buy'] == True].index[0] if buy_signals > 0 else None
        
        # Calculate trend capture
        if first_buy_idx is not None:
            entry_price = result_df.loc[first_buy_idx, 'close']
            exit_price = result_df['close'].iloc[-1]
            trend_profit = exit_price - entry_price
            trend_capture_pct = (trend_profit / (df['close'].iloc[-1] - df['open'].iloc[0])) * 100
        else:
            entry_price = 0
            trend_profit = 0
            trend_capture_pct = 0
        
        result = {
            'name': config['name'],
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'early_buy_signals': early_buy_signals,
            'first_buy_idx': first_buy_idx,
            'entry_price': entry_price,
            'trend_profit': trend_profit,
            'trend_capture_pct': trend_capture_pct
        }
        
        results.append(result)
        
        print(f"   📊 Buy: {buy_signals}, Sell: {sell_signals}, Early Buy: {early_buy_signals}")
        if first_buy_idx is not None:
            print(f"   📈 First buy at brick {first_buy_idx}: {entry_price:.1f}")
            print(f"   💰 Trend profit: +{trend_profit:.1f} points ({trend_capture_pct:.1f}% of total trend)")
        else:
            print(f"   ❌ No buy signals generated")
    
    # Summary
    print(f"\n📊 ENHANCED UT BOT COMPARISON")
    print("=" * 80)
    
    best_result = max(results, key=lambda x: x['trend_capture_pct'])
    
    for result in results:
        marker = "🏆" if result == best_result else "  "
        profit_str = f"+{result['trend_profit']:.1f}" if result['trend_profit'] > 0 else f"{result['trend_profit']:.1f}"
        
        print(f"{marker} {result['name']:20s} | "
              f"Buy:{result['buy_signals']:2d} Sell:{result['sell_signals']:2d} Early:{result['early_buy_signals']:2d} | "
              f"Entry@{result['first_buy_idx'] or 0:3d} | "
              f"Profit:{profit_str:6s} | "
              f"Capture:{result['trend_capture_pct']:5.1f}%")
    
    print(f"\n🎯 BEST CONFIGURATION:")
    print(f"🏆 {best_result['name']}")
    print(f"   Captured {best_result['trend_capture_pct']:.1f}% of the trend")
    print(f"   Entry at brick {best_result['first_buy_idx']} (earlier = better)")
    print(f"   Profit potential: +{best_result['trend_profit']:.1f} points")
    
    return results, best_result

def main():
    """Main function"""
    try:
        results, best_config = test_enhanced_utbot()
        
        print(f"\n🎉 ENHANCED UT BOT TESTING COMPLETED!")
        print(f"💡 The enhanced version should capture trends much earlier!")
        
    except Exception as e:
        print(f"\n💥 Testing error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
