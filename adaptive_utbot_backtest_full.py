import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import os
import math
import sys

# Add the qt-meta directory to the path
sys.path.append('qt-meta')
from ut_bot_adaptation import UTBot

# Set up logging
log_dir = "strategy_logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_filename = f"{log_dir}/adaptive_ut_bot_strategy_{timestamp}.log"

# Function to log messages
def log_message(message):
    """Log message to file and print to console"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_msg = f"[{timestamp}] {message}"
    print(log_msg)
    with open(log_filename, 'a') as f:
        f.write(log_msg + '\n')

log_message("=== ADAPTIVE UT BOT TRADING STRATEGY BACKTEST ===")

# Load the Renko data for backtesting
log_message("Loading 7-day stpRNG Renko data...")
renko_df = pd.read_csv('qt-meta/stpRNG_7days_renko_0_1.csv')
renko_df['datetime'] = pd.to_datetime(renko_df['datetime'])
log_message(f"7-day stpRNG Renko data shape: {renko_df.shape}")

# Adaptive UT Bot Class
class AdaptiveUTBotStrategy:
    def __init__(self, lookback_period=100, trend_threshold=15, strength_threshold=0.002):
        self.original_utbot = UTBot(atr_period=1, sensitivity=1)
        self.enhanced_utbot = UTBot(atr_period=1, sensitivity=1)  # Will modify signals
        self.lookback_period = lookback_period
        self.trend_threshold = trend_threshold
        self.strength_threshold = strength_threshold
        self.current_regime = "ranging"
        self.regime_switches = 0
        
    def detect_market_regime(self, df, current_idx):
        """Detect if market is trending or ranging"""
        start_idx = max(0, current_idx - self.lookback_period)
        window_data = df.iloc[start_idx:current_idx+1].copy()
        
        if len(window_data) < 20:
            return "ranging"
        
        # Calculate price movement
        price_movement = abs(window_data['close'].iloc[-1] - window_data['open'].iloc[0])
        
        # Calculate trend strength using EMAs
        window_data['ema_fast'] = window_data['close'].ewm(span=5).mean()
        window_data['ema_slow'] = window_data['close'].ewm(span=20).mean()
        
        trend_strength = abs(window_data['ema_fast'].iloc[-1] - window_data['ema_slow'].iloc[-1]) / window_data['close'].iloc[-1]
        
        # Decision logic
        if price_movement > self.trend_threshold and trend_strength > self.strength_threshold:
            return "trending"
        else:
            return "ranging"
    
    def generate_enhanced_signals(self, df):
        """Generate enhanced trend-following signals"""
        # Get original signals
        original_signals = self.original_utbot.run(df)
        
        # Calculate trend indicators
        df_copy = df.copy()
        df_copy['ema_fast'] = df_copy['close'].ewm(span=5).mean()
        df_copy['ema_slow'] = df_copy['close'].ewm(span=20).mean()
        df_copy['uptrend'] = (df_copy['close'] > df_copy['ema_fast']) & (df_copy['close'] > df_copy['ema_slow'])
        df_copy['downtrend'] = (df_copy['close'] < df_copy['ema_fast']) & (df_copy['close'] < df_copy['ema_slow'])
        df_copy['momentum'] = df_copy['close'].pct_change(periods=5).fillna(0)
        
        # Enhanced signals
        enhanced_signals = original_signals.copy()
        
        # Filter buy signals: only in uptrends with positive momentum
        enhanced_buy = (
            original_signals['buy'] & 
            df_copy['uptrend'] & 
            (df_copy['momentum'] > 0)
        )
        
        # Filter sell signals: only in downtrends with negative momentum
        enhanced_sell = (
            original_signals['sell'] & 
            df_copy['downtrend'] & 
            (df_copy['momentum'] < 0)
        )
        
        # Early trend entry signals
        early_buy = (
            (df_copy['ema_fast'] > df_copy['ema_slow']) &
            (df_copy['ema_fast'].shift(1) <= df_copy['ema_slow'].shift(1)) &
            (df_copy['close'] > df_copy['ema_fast'])
        )
        
        early_sell = (
            (df_copy['ema_fast'] < df_copy['ema_slow']) &
            (df_copy['ema_fast'].shift(1) >= df_copy['ema_slow'].shift(1)) &
            (df_copy['close'] < df_copy['ema_fast'])
        )
        
        # Combine signals
        enhanced_signals['buy'] = early_buy | enhanced_buy
        enhanced_signals['sell'] = early_sell | enhanced_sell
        
        return enhanced_signals
    
    def get_signals(self, df, current_idx):
        """Get signals based on current market regime"""
        # Check if regime should change
        new_regime = self.detect_market_regime(df, current_idx)
        
        if new_regime != self.current_regime:
            self.regime_switches += 1
            log_message(f"🔄 REGIME CHANGE at index {current_idx}: {self.current_regime.upper()} → {new_regime.upper()}")
            self.current_regime = new_regime
        
        # Generate signals based on regime
        if self.current_regime == "trending":
            signals = self.generate_enhanced_signals(df.iloc[:current_idx+1])
            strategy_name = "Enhanced"
        else:
            signals = self.original_utbot.run(df.iloc[:current_idx+1])
            strategy_name = "Original"
        
        return signals, strategy_name

# Initialize adaptive strategy
adaptive_strategy = AdaptiveUTBotStrategy(
    lookback_period=100,
    trend_threshold=15,
    strength_threshold=0.002
)

log_message("Initialized Adaptive UT Bot Strategy")
log_message(f"Lookback Period: {adaptive_strategy.lookback_period}")
log_message(f"Trend Threshold: {adaptive_strategy.trend_threshold} points")
log_message(f"Strength Threshold: {adaptive_strategy.strength_threshold}")

# Dynamic risk management function
def calculate_dynamic_risk(equity, equity_history, win_streak, loss_streak, recent_outcomes=None):
    """Calculate dynamic risk percentage based on multiple factors"""
    base_risk = 0.04  # Base risk percentage

    # 1. Streak-based adjustment
    if win_streak >= 5:
        streak_factor = 1.3
    elif win_streak >= 3:
        streak_factor = 1.15
    elif loss_streak >= 3:
        streak_factor = 0.7
    elif loss_streak >= 1:
        streak_factor = 0.85
    else:
        streak_factor = 1.0

    # 2. Equity milestone adjustment
    if equity < 1000:
        equity_factor = 1.0
    elif equity < 10000:
        equity_factor = 0.95
    elif equity < 100000:
        equity_factor = 0.9
    elif equity < 1000000:
        equity_factor = 0.85
    else:
        equity_factor = 0.8

    # 3. Recent performance adjustment
    if recent_outcomes and len(recent_outcomes) >= 20:
        win_rate = sum(1 for t in recent_outcomes[-20:] if t > 0) / 20
        if win_rate > 0.9:
            performance_factor = 1.2
        elif win_rate > 0.8:
            performance_factor = 1.1
        elif win_rate < 0.5:
            performance_factor = 0.8
        else:
            performance_factor = 1.0
    else:
        performance_factor = 1.0

    # 4. Drawdown protection
    if len(equity_history) > 1:
        peak = max(equity_history)
        drawdown = (peak - equity) / peak
        if drawdown > 0.2:
            drawdown_factor = 0.6
        elif drawdown > 0.1:
            drawdown_factor = 0.8
        else:
            drawdown_factor = 1.0
    else:
        drawdown_factor = 1.0

    # Calculate final risk percentage
    risk = base_risk * streak_factor * equity_factor * performance_factor * drawdown_factor

    # Ensure risk stays within reasonable bounds
    return max(0.01, min(risk, 0.06))  # Cap between 1% and 6%

# Position sizing function
def calculate_position_size(risk_amount, price_risk, max_per_position, max_total_vol, current_open_vol):
    """Calculate optimal position allocation respecting volume constraints"""
    # Calculate ideal lot size
    ideal_lot_size = risk_amount / (price_risk * 10) # Multiplier 10 from original script

    # Available volume within total limit
    available_vol = max_total_vol - current_open_vol

    # Check if we need multiple positions
    if ideal_lot_size <= max_per_position:
        # Single position is sufficient
        lot_size = min(ideal_lot_size, max_per_position, available_vol)
        num_positions = 1 if lot_size > 0 else 0
    else:
        # Need multiple positions
        max_possible_vol = min(ideal_lot_size, available_vol)
        if max_possible_vol <= 0:
            return 0, 0

        num_positions = math.ceil(max_possible_vol / max_per_position)
        lot_size = min(max_per_position, max_possible_vol / num_positions)

    return num_positions, lot_size

# Strategy parameters
BRICK_SIZE = 0.1
SPREAD = 0.0
COMMISSION_RATE = 0.15  # 15% commission on profits
MIN_VOL = 0.10
MAX_VOL_PER_POS = 50.0
MAX_TOTAL_VOL = 200.0

def apply_commission(profit):
    """Apply 15% commission on profits only. Losses are not charged commission."""
    if profit > 0:
        commission = profit * COMMISSION_RATE
        net_profit = profit - commission
        return net_profit, commission
    else:
        return profit, 0.0  # No commission on losses

# Account state
equity = 10.0  # Starting with $10
open_volume = 0
equity_history = [equity]
win_streak = 0
loss_streak = 0
trade_outcomes = []
win_count = 0
trade_count = 0
total_commission = 0.0

# Trading results
trades = []

log_message(f"Starting Adaptive Backtest with ${equity:.2f}")
log_message("Beginning trade execution...")

# Backtest loop
i = 100  # Start after enough data for regime detection
while i < len(renko_df) - 10:  # Leave room at the end for trade management
    # Get adaptive signals
    signals_df, strategy_used = adaptive_strategy.get_signals(renko_df, i)

    if i >= len(signals_df):
        i += 1
        continue

    current_row = renko_df.iloc[i]
    current_signals = signals_df.iloc[i] if i < len(signals_df) else None

    if current_signals is None:
        i += 1
        continue

    # Check for buy/sell signals
    if current_signals['buy'] or current_signals['sell']:

        # Calculate dynamic risk
        risk_percentage = calculate_dynamic_risk(
            equity,
            equity_history,
            win_streak,
            loss_streak,
            trade_outcomes
        )

        # Calculate risk amount and price risk
        price_risk = 2 * BRICK_SIZE + SPREAD
        risk_amount = equity * risk_percentage

        # Calculate position size
        num_positions, lot_size = calculate_position_size(
            risk_amount,
            price_risk,
            MAX_VOL_PER_POS,
            MAX_TOTAL_VOL,
            open_volume
        )

        if num_positions > 0 and lot_size >= MIN_VOL:
            # Execute trade
            entry_time = current_row['datetime']
            entry_price = current_row['close']
            position_type = "LONG" if current_signals['buy'] else "SHORT"
            position_lot_size = lot_size
            open_volume += position_lot_size
            trade_count += 1

            log_message(f"Trade #{trade_count} executed - {position_type} at {entry_time}, price: {entry_price}, volume: {position_lot_size:.2f} | Strategy: {strategy_used} | Regime: {adaptive_strategy.current_regime}")

            # Trade management
            tp_bricks = 5  # Take profit at 5 bricks
            sl_bricks = 2  # Stop loss at 2 bricks

            # Initialize trade outcome variables
            profit = 0
            trade_commission = 0.0
            outcome = None
            exit_price = entry_price
            exit_time = entry_time

            # Simulate trade - look ahead for exit conditions
            for j in range(i + 1, min(i + 20, len(renko_df))):
                move = renko_df.iloc[j]['direction']
                current_price_at_exit_check = renko_df.iloc[j]['close']

                if position_type == "LONG":
                    if move == 'up':
                        tp_bricks -= 1
                        if tp_bricks == 0:
                            gross_profit = (5 * BRICK_SIZE - SPREAD) * 10 * position_lot_size
                            profit, trade_commission = apply_commission(gross_profit)
                            outcome = 'LONG_TP'
                            exit_price = current_price_at_exit_check
                            exit_time = renko_df.iloc[j]['datetime']
                            log_message(f"LONG_TP hit. Gross: ${gross_profit:.2f}, Commission: ${trade_commission:.2f}, Net: ${profit:.2f}")
                            break
                    elif move == 'down':
                        sl_bricks -= 1
                        if sl_bricks == 0:
                            outcome = 'LONG_SL'
                            log_message("LONG_SL hit, preparing for short reversal")

                            # Short reversal logic
                            reversal_tp_bricks = 2
                            reversal_sl_bricks = 5

                            for k in range(j + 1, min(j + 20, len(renko_df))):
                                reversal_move = renko_df.iloc[k]['direction']
                                reversal_price_at_exit_check = renko_df.iloc[k]['close']

                                if reversal_move == 'down':
                                    reversal_tp_bricks -= 1
                                    if reversal_tp_bricks == 0:
                                        gross_profit = (2 * BRICK_SIZE - SPREAD) * 10 * position_lot_size
                                        profit, trade_commission = apply_commission(gross_profit)
                                        outcome = 'SHORT_TP_AFTER_LONG_SL'
                                        exit_price = reversal_price_at_exit_check
                                        exit_time = renko_df.iloc[k]['datetime']
                                        log_message(f"SHORT_TP hit after LONG_SL. Gross: ${gross_profit:.2f}, Commission: ${trade_commission:.2f}, Net: ${profit:.2f}")
                                        break
                                elif reversal_move == 'up':
                                    reversal_sl_bricks -= 1
                                    if reversal_sl_bricks == 0:
                                        profit = -(5 * BRICK_SIZE + SPREAD) * 10 * position_lot_size
                                        outcome = 'SHORT_SL_AFTER_LONG_SL'
                                        exit_price = reversal_price_at_exit_check
                                        exit_time = renko_df.iloc[k]['datetime']
                                        log_message(f"SHORT_SL hit after LONG_SL. Loss: ${profit:.2f}")
                                        break

                            if outcome == 'LONG_SL':
                                profit = -(2 * BRICK_SIZE + SPREAD) * 10 * position_lot_size
                                exit_price = current_price_at_exit_check
                                exit_time = renko_df.iloc[j]['datetime']
                            break

                elif position_type == "SHORT":
                    if move == 'down':
                        tp_bricks -= 1
                        if tp_bricks == 0:
                            gross_profit = (5 * BRICK_SIZE - SPREAD) * 10 * position_lot_size
                            profit, trade_commission = apply_commission(gross_profit)
                            outcome = 'SHORT_TP'
                            exit_price = current_price_at_exit_check
                            exit_time = renko_df.iloc[j]['datetime']
                            log_message(f"SHORT_TP hit. Gross: ${gross_profit:.2f}, Commission: ${trade_commission:.2f}, Net: ${profit:.2f}")
                            break
                    elif move == 'up':
                        sl_bricks -= 1
                        if sl_bricks == 0:
                            outcome = 'SHORT_SL'
                            log_message("SHORT_SL hit, preparing for long reversal")

                            # Long reversal logic
                            reversal_tp_bricks = 2
                            reversal_sl_bricks = 5

                            for k in range(j + 1, min(j + 20, len(renko_df))):
                                reversal_move = renko_df.iloc[k]['direction']
                                reversal_price_at_exit_check = renko_df.iloc[k]['close']

                                if reversal_move == 'up':
                                    reversal_tp_bricks -= 1
                                    if reversal_tp_bricks == 0:
                                        gross_profit = (2 * BRICK_SIZE - SPREAD) * 10 * position_lot_size
                                        profit, trade_commission = apply_commission(gross_profit)
                                        outcome = 'LONG_TP_AFTER_SHORT_SL'
                                        exit_price = reversal_price_at_exit_check
                                        exit_time = renko_df.iloc[k]['datetime']
                                        log_message(f"LONG_TP hit after SHORT_SL. Gross: ${gross_profit:.2f}, Commission: ${trade_commission:.2f}, Net: ${profit:.2f}")
                                        break
                                elif reversal_move == 'down':
                                    reversal_sl_bricks -= 1
                                    if reversal_sl_bricks == 0:
                                        profit = -(5 * BRICK_SIZE + SPREAD) * 10 * position_lot_size
                                        outcome = 'LONG_SL_AFTER_SHORT_SL'
                                        exit_price = reversal_price_at_exit_check
                                        exit_time = renko_df.iloc[k]['datetime']
                                        log_message(f"LONG_SL hit after SHORT_SL. Loss: ${profit:.2f}")
                                        break

                            if outcome == 'SHORT_SL':
                                profit = -(2 * BRICK_SIZE + SPREAD) * 10 * position_lot_size
                                exit_price = current_price_at_exit_check
                                exit_time = renko_df.iloc[j]['datetime']
                            break

            # Time-based exit if no other exit condition met
            if profit == 0:
                exit_bar_index = min(i + 10, len(renko_df) - 1)
                exit_price = renko_df.iloc[exit_bar_index]['close']
                exit_time = renko_df.iloc[exit_bar_index]['datetime']

                if position_type == "LONG":
                    gross_profit = (exit_price - entry_price) * 10 * position_lot_size
                else:  # SHORT
                    gross_profit = (entry_price - exit_price) * 10 * position_lot_size

                profit, trade_commission = apply_commission(gross_profit)
                outcome = 'TIME_EXIT'

                if trade_commission > 0:
                    log_message(f"Time exit taken. Gross: ${gross_profit:.2f}, Commission: ${trade_commission:.2f}, Net: ${profit:.2f}")
                else:
                    log_message(f"Time exit taken. Loss: ${profit:.2f} (no commission on losses)")

            # Update account state
            previous_equity = equity
            equity += profit
            total_commission += trade_commission
            equity_history.append(equity)
            trade_outcomes.append(profit)

            # Safety check
            if equity < 0:
                log_message(f"CRITICAL: Balance went negative (${equity:.2f}). Stopping trading immediately!")
                break

            # Update win/loss streaks
            if profit > 0:
                win_streak += 1
                loss_streak = 0
                win_count += 1
            else:
                win_streak = 0
                loss_streak += 1

            # Log trade completion
            log_message(f"Trade #{trade_count} completed:")
            log_message(f"Entry Time: {entry_time}")
            log_message(f"Exit Time: {exit_time}")
            log_message(f"Outcome: {outcome}")
            log_message(f"Profit: ${profit:.2f}")
            log_message(f"Equity: ${previous_equity:.2f} -> ${equity:.2f}")
            log_message(f"Strategy: {strategy_used} | Regime: {adaptive_strategy.current_regime}")

            # Record trade
            trades.append({
                'entry_time': entry_time,
                'entry_price': entry_price,
                'position_type': position_type,
                'volume': round(position_lot_size, 2),
                'exit_time': exit_time,
                'exit_price': exit_price,
                'outcome': outcome,
                'profit': round(profit, 2),
                'commission': round(trade_commission, 2),
                'balance': round(equity, 2),
                'risk_percentage': round(risk_percentage, 4),
                'strategy': strategy_used,
                'regime': adaptive_strategy.current_regime
            })

            # Update open volume
            open_volume -= position_lot_size

            # Move to next bar after exit
            try:
                exit_idx = renko_df[renko_df['datetime'] == exit_time].index[0]
                i = renko_df.index.get_loc(exit_idx) + 1
            except:
                i += 1
        else:
            i += 1
    else:
        i += 1

# Final results and logging
log_message(f"\n=== ADAPTIVE STRATEGY FINAL RESULTS ===")
log_message(f"Regime Switches: {adaptive_strategy.regime_switches}")

# Save results
results_df = pd.DataFrame(trades)
if not results_df.empty:
    results_df.to_csv(f"{log_dir}/adaptive_ut_bot_results_{timestamp}.csv", index=False)
    log_message(f"Saved results to {log_dir}/adaptive_ut_bot_results_{timestamp}.csv")

# Calculate performance metrics
win_rate = (results_df['profit'] > 0).mean() * 100 if len(results_df) > 0 else 0
profit_factor = results_df[results_df['profit'] > 0]['profit'].sum() / abs(results_df[results_df['profit'] < 0]['profit'].sum() + 1e-6) if len(results_df) > 0 else 0

log_message(f"Trades Executed: {trade_count}")
log_message(f"Final Balance: ${equity:.2f}")
log_message(f"Total Commission Paid: ${total_commission:.2f}")
log_message(f"Win Rate: {win_rate:.2f}%")
log_message(f"Profit Factor: {profit_factor:.2f}")

# Strategy breakdown
if not results_df.empty:
    original_trades = results_df[results_df['strategy'] == 'Original']
    enhanced_trades = results_df[results_df['strategy'] == 'Enhanced']

    if len(original_trades) > 0:
        orig_profit = original_trades['profit'].sum()
        orig_win_rate = (original_trades['profit'] > 0).mean() * 100
        log_message(f"Original Strategy: {len(original_trades)} trades, ${orig_profit:.2f} profit, {orig_win_rate:.1f}% win rate")

    if len(enhanced_trades) > 0:
        enh_profit = enhanced_trades['profit'].sum()
        enh_win_rate = (enhanced_trades['profit'] > 0).mean() * 100
        log_message(f"Enhanced Strategy: {len(enhanced_trades)} trades, ${enh_profit:.2f} profit, {enh_win_rate:.1f}% win rate")

log_message("Adaptive UT Bot strategy backtest completed successfully!")
