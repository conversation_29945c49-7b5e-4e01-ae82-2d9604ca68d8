#!/usr/bin/env python3
"""
Simple Order Test Script
- Vectorized 0.1 Renko bricks from 1-second ticks
- Real FOK order placement test
- No emojis, minimal logging
"""

import MetaTrader5 as mt5
import numpy as np
import time
from datetime import datetime

class SimpleRenko:
    """Simple vectorized Renko builder"""
    
    def __init__(self, brick_size=0.1):
        self.brick_size = brick_size
        self.bricks = []
        self.current_price = None
        self.last_brick_close = None
    
    def add_tick(self, price):
        """Add tick and return new bricks"""
        if self.current_price is None:
            self.current_price = price
            self.last_brick_close = price
            return []
        
        price_diff = price - self.last_brick_close
        
        if abs(price_diff) >= self.brick_size:
            num_bricks = int(abs(price_diff) / self.brick_size)
            direction = 1 if price_diff > 0 else -1
            
            new_bricks = []
            for i in range(num_bricks):
                if direction > 0:
                    brick_close = self.last_brick_close + self.brick_size
                    brick = {
                        'open': self.last_brick_close,
                        'close': brick_close,
                        'direction': 'up'
                    }
                else:
                    brick_close = self.last_brick_close - self.brick_size
                    brick = {
                        'open': self.last_brick_close,
                        'close': brick_close,
                        'direction': 'down'
                    }
                
                new_bricks.append(brick)
                self.bricks.append(brick)
                self.last_brick_close = brick_close
            
            return new_bricks
        
        return []

def connect_mt5():
    """Connect to MT5"""
    if not mt5.initialize():
        print(f"MT5 init failed: {mt5.last_error()}")
        return False
    
    login = 40496559
    password = "@Ripper25"
    server = "Deriv-Demo"
    
    if not mt5.login(login, password, server):
        print(f"Login failed: {mt5.last_error()}")
        mt5.shutdown()
        return False
    
    print(f"Connected to {login} on {server}")
    return True

def find_symbol():
    """Find Step Index symbol"""
    symbols = ["Step Index", "STEP Index", "Step_Index"]

    for symbol in symbols:
        info = mt5.symbol_info(symbol)
        if info:
            print(f"Found symbol: {symbol}")
            print(f"Bid: {info.bid}, Ask: {info.ask}")
            print(f"Min volume: {info.volume_min}")
            print(f"Max volume: {info.volume_max}")
            print(f"Volume step: {info.volume_step}")
            return symbol

    print("Symbol not found")
    return None

def send_test_order(symbol, order_type, volume=0.10):
    """Send test FOK order with execution timing"""
    start_time = time.perf_counter()

    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        print("Failed to get tick")
        return None, 0

    if order_type == "BUY":
        action = mt5.ORDER_TYPE_BUY
        price = tick.ask
    else:
        action = mt5.ORDER_TYPE_SELL
        price = tick.bid

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": volume,
        "type": action,
        "price": price,
        "deviation": 20,
        "magic": 12345,
        "comment": f"Test {order_type}",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK,
    }

    print(f"Sending {order_type} order: {volume} lots at {price}")

    result = mt5.order_send(request)
    execution_time = (time.perf_counter() - start_time) * 1000  # ms

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        print(f"Order failed: {result.retcode} - {result.comment}")
        return None, execution_time

    print(f"Order success: Ticket {result.order}, Price {result.price}, Time: {execution_time:.1f}ms")
    return result, execution_time

def close_position(ticket):
    """Close position by ticket with execution timing"""
    start_time = time.perf_counter()

    positions = mt5.positions_get(ticket=ticket)
    if not positions:
        print(f"Position {ticket} not found")
        return False, 0

    position = positions[0]
    symbol = position.symbol

    if position.type == mt5.POSITION_TYPE_BUY:
        order_type = mt5.ORDER_TYPE_SELL
        price = mt5.symbol_info_tick(symbol).bid
    else:
        order_type = mt5.ORDER_TYPE_BUY
        price = mt5.symbol_info_tick(symbol).ask

    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": position.volume,
        "type": order_type,
        "position": ticket,
        "price": price,
        "deviation": 20,
        "magic": 12345,
        "comment": "Close test",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK,
    }

    result = mt5.order_send(request)
    execution_time = (time.perf_counter() - start_time) * 1000  # ms

    if result.retcode != mt5.TRADE_RETCODE_DONE:
        print(f"Close failed: {result.retcode}")
        return False, execution_time

    print(f"Position {ticket} closed, Time: {execution_time:.1f}ms")
    return True, execution_time

def main():
    """Main test function"""
    print("Simple Order Test Script")
    print("Vectorized 0.1 Renko + FOK Orders")
    print("-" * 40)
    
    # Connect to MT5
    if not connect_mt5():
        return
    
    # Find symbol
    symbol = find_symbol()
    if not symbol:
        mt5.shutdown()
        return
    
    # Initialize Renko
    renko = SimpleRenko(brick_size=0.1)
    
    print("\nStarting tick collection and order test...")
    print("Will place test order after 2 Renko bricks")
    print("Will close position after 2 more bricks")
    print("Press Ctrl+C to stop")

    tick_count = 0
    brick_count = 0
    last_tick_time = None
    test_order_placed = False
    test_ticket = None
    open_execution_times = []
    close_execution_times = []

    try:
        while True:
            # Get tick
            tick = mt5.symbol_info_tick(symbol)
            if not tick:
                continue

            # Check for new tick
            if last_tick_time == tick.time:
                time.sleep(0.1)
                continue

            last_tick_time = tick.time
            tick_count += 1

            # Add to Renko
            new_bricks = renko.add_tick(tick.bid)

            if new_bricks:
                brick_count += len(new_bricks)
                print(f"Tick {tick_count}: {tick.bid:.5f} -> {len(new_bricks)} brick(s), Total: {brick_count}")

                # Place test order after 2 bricks
                if brick_count >= 2 and not test_order_placed:
                    print("\n2 bricks reached - placing test BUY order...")
                    result, exec_time = send_test_order(symbol, "BUY", 0.10)
                    open_execution_times.append(exec_time)

                    if result:
                        test_order_placed = True
                        test_ticket = result.order
                        print("Test order placed successfully")
                    else:
                        print("Test order failed")

                # Close test order after 4 bricks (2 more bricks)
                if brick_count >= 4 and test_ticket:
                    print("\n4 bricks reached - closing test order...")
                    success, exec_time = close_position(test_ticket)
                    close_execution_times.append(exec_time)

                    if success:
                        print("Test completed successfully")
                        break
                    else:
                        print("Failed to close test order")

            # Show progress every 10 ticks
            if tick_count % 10 == 0:
                print(f"Processed {tick_count} ticks, {brick_count} bricks")

            time.sleep(0.1)

    except KeyboardInterrupt:
        print("\nTest stopped by user")

        # Close any open test position
        if test_ticket:
            print("Closing test position...")
            success, exec_time = close_position(test_ticket)
            if exec_time > 0:
                close_execution_times.append(exec_time)
    
    # Calculate execution statistics
    print(f"\nTest Summary:")
    print(f"Ticks processed: {tick_count}")
    print(f"Renko bricks: {brick_count}")
    print(f"Test order: {'Placed and closed' if test_order_placed else 'Not placed'}")

    if open_execution_times:
        avg_open = sum(open_execution_times) / len(open_execution_times)
        print(f"Order open times: {open_execution_times}")
        print(f"Average open time: {avg_open:.1f}ms")

    if close_execution_times:
        avg_close = sum(close_execution_times) / len(close_execution_times)
        print(f"Order close times: {close_execution_times}")
        print(f"Average close time: {avg_close:.1f}ms")

    if open_execution_times and close_execution_times:
        all_times = open_execution_times + close_execution_times
        overall_avg = sum(all_times) / len(all_times)
        print(f"Overall average execution time: {overall_avg:.1f}ms")
        print(f"Speed rating: {'EXCELLENT' if overall_avg < 100 else 'GOOD' if overall_avg < 200 else 'ACCEPTABLE' if overall_avg < 500 else 'SLOW'}")

    # Disconnect
    mt5.shutdown()
    print("Test completed")

if __name__ == "__main__":
    main()
